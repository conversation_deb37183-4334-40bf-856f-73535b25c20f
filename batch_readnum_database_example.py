# coding:utf-8
# batch_readnum_database_example.py
"""
带数据库功能的批量阅读量爬虫示例
演示如何使用BatchReadnumSpider将阅读量数据实时保存到数据库
"""

import logging
import sys
from batch_readnum_spider import BatchReadnumSpider
from database_manager import DatabaseManager
from read_cookie import ReadCookie

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('batch_readnum_database.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

def main():
    """主函数"""
    print("🚀 启动带数据库功能的批量阅读量爬虫")
    print("=" * 60)
    
    # 数据库配置
    db_config = {
        'host': '127.0.0.1',
        'port': 3306,
        'user': 'root',
        'password': 'root',
        'database': 'xuanfa'
    }
    
    # 测试数据库连接
    print("🔍 测试数据库连接...")
    try:
        with DatabaseManager(**db_config) as db:
            count = db.get_articles_count()
            print(f"✅ 数据库连接成功！当前数据库中有 {count} 篇文章")
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        print("请检查数据库配置和连接")
        return
    
    # 获取Cookie和认证信息
    print("\n📝 获取认证信息...")
    
    # 方法1: 从文件读取Cookie
    cookie_reader = ReadCookie(outfile="wechat_keys.txt", delete_existing_file=False)
    
    try:
        # 尝试从现有文件加载Cookie
        auth_info = cookie_reader.load_auth_info()
        
        if not auth_info:
            print("❌ 未找到有效的认证信息")
            print("请先运行Cookie抓取器获取认证信息，或手动输入")
            
            # 手动输入认证信息
            print("\n📝 请手动输入认证信息:")
            appmsg_token = input("请输入 appmsg_token: ").strip()
            biz = input("请输入 biz: ").strip()
            cookie_str = input("请输入 cookie: ").strip()
            
            if not all([appmsg_token, biz, cookie_str]):
                print("❌ 认证信息不完整")
                return
            
            auth_info = {
                'appmsg_token': appmsg_token,
                'biz': biz,
                'cookie_str': cookie_str,
                'headers': {}
            }
        
        print("✅ 认证信息加载成功")
        
    except Exception as e:
        print(f"❌ 加载认证信息失败: {e}")
        return
    
    # 获取公众号名称
    unit_name = input("\n请输入公众号名称 (单位名称): ").strip()
    if not unit_name:
        unit_name = "未知公众号"
    
    # 爬取参数配置
    try:
        max_pages = int(input("请输入最大页数 (默认3): ") or "3")
        articles_per_page = int(input("请输入每页文章数 (默认10): ") or "10")
        days_back = int(input("请输入抓取天数 (默认7): ") or "7")
    except ValueError:
        print("❌ 参数必须是数字")
        return
    
    print(f"\n🎯 开始批量抓取阅读量数据")
    print(f"📄 公众号: {unit_name}")
    print(f"📊 参数: 最大{max_pages}页，每页{articles_per_page}篇，{days_back}天内文章")
    print(f"💾 数据库保存: 是")
    print("=" * 60)
    
    # 创建爬虫实例
    try:
        spider = BatchReadnumSpider(
            auth_info=auth_info,
            save_to_db=True,        # 启用数据库保存
            db_config=db_config,    # 数据库配置
            unit_name=unit_name     # 公众号名称
        )
        
        # 开始批量抓取
        results = spider.batch_crawl_readnum(
            max_pages=max_pages,
            articles_per_page=articles_per_page,
            days_back=days_back
        )
        
        if results:
            # 显示统计摘要
            summary = spider.generate_summary_report()
            if summary:
                print("\n📊 抓取统计摘要:")
                print(f"📖 总文章数: {summary['total_articles']}")
                print(f"📈 总阅读量: {summary['total_reads']:,}")
                print(f"👍 总点赞数: {summary['total_likes']:,}")
                print(f"📊 平均阅读量: {summary['avg_reads']:,.0f}")
                print(f"📅 时间范围: {summary['date_range']['start']} ~ {summary['date_range']['end']}")
            
            # 同时保存到文件
            print("\n💾 保存数据到文件...")
            excel_file = spider.save_to_excel()
            json_file = spider.save_to_json()
            
            if excel_file:
                print(f"📊 Excel文件: {excel_file}")
            if json_file:
                print(f"💾 JSON文件: {json_file}")
            
            # 显示数据库统计
            print("\n💾 数据库统计:")
            try:
                with DatabaseManager(**db_config) as db:
                    total_count = db.get_articles_count()
                    print(f"📊 数据库中总文章数: {total_count}")
            except Exception as e:
                print(f"❌ 获取数据库统计失败: {e}")
            
            print("\n🎉 批量抓取任务完成！")
            
        else:
            print("❌ 未获取到任何数据")
            
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断抓取")
    except Exception as e:
        print(f"❌ 抓取过程中出错: {e}")
        logging.error(f"抓取错误: {e}", exc_info=True)

def test_readnum_database():
    """测试阅读量数据库功能"""
    print("🧪 测试阅读量数据库功能")
    print("=" * 40)
    
    db_config = {
        'host': '127.0.0.1',
        'port': 3306,
        'user': 'root',
        'password': 'root',
        'database': 'xuanfa'
    }
    
    try:
        with DatabaseManager(**db_config) as db:
            # 测试连接
            print("✅ 数据库连接测试通过")
            
            # 获取当前文章数量
            count = db.get_articles_count()
            print(f"📊 当前数据库中有 {count} 篇文章")
            
            # 测试插入一条带阅读量的示例数据
            test_article = {
                'title': '测试阅读量文章标题',
                'content': '这是一篇测试阅读量文章的内容...',
                'url': 'https://mp.weixin.qq.com/s/readnum_test123456',
                'pub_time': '2025-08-05 20:00:00',
                'crawl_time': '2025-08-05 22:30:00',
                'unit_name': '测试阅读量公众号',
                'view_count': 5000  # 阅读量
            }
            
            # 检查是否已存在
            if not db.check_article_exists(test_article['url']):
                success = db.insert_article(test_article)
                if success:
                    print("✅ 测试阅读量文章插入成功")
                    new_count = db.get_articles_count()
                    print(f"📊 插入后数据库中有 {new_count} 篇文章")
                else:
                    print("❌ 测试阅读量文章插入失败")
            else:
                print("⚠️ 测试阅读量文章已存在，跳过插入")
            
    except Exception as e:
        print(f"❌ 阅读量数据库测试失败: {e}")

if __name__ == "__main__":
    print("请选择操作:")
    print("1. 运行批量阅读量爬虫 (带数据库保存)")
    print("2. 测试阅读量数据库操作")
    
    choice = input("请输入选择 (1/2): ").strip()
    
    if choice == "1":
        main()
    elif choice == "2":
        test_readnum_database()
    else:
        print("❌ 无效选择")
