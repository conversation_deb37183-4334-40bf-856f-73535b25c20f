2025-08-05 22:07:50,045 - INFO - ================================================================================
2025-08-05 22:07:50,045 - INFO - 🚀 微信公众号全自动爬取流程启动 🚀
2025-08-05 22:07:50,046 - INFO - ================================================================================
2025-08-05 22:07:50,046 - INFO - 版本: v3.0 - 全自动化版本
2025-08-05 22:07:50,046 - INFO - 设计用途: Windows任务计划程序自动执行
2025-08-05 22:07:50,046 - INFO - 执行时间: 2025-08-05 22:07:50
2025-08-05 22:07:50,046 - INFO - ================================================================================
2025-08-05 22:07:50,047 - INFO - 启动全新自动化爬取流程...
2025-08-05 22:07:50,047 - INFO - ================================================================================
2025-08-05 22:07:50,048 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-05 22:07:50,048 - INFO - ================================================================================
2025-08-05 22:07:50,048 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-05 22:07:50,131 - INFO - 找到有效目标 1: 钟山清风 - https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&...
2025-08-05 22:07:50,131 - INFO - 找到有效目标 2: 南京党建 - http://mp.weixin.qq.com/s?__biz=MzI2OTMwNzU5Nw==&m...
2025-08-05 22:07:50,131 - INFO - 找到有效目标 3: 南京发布 - http://mp.weixin.qq.com/s?__biz=MjM5MTczODg0MA==&m...
2025-08-05 22:07:50,131 - INFO - 共找到 3 个有效的公众号目标
2025-08-05 22:07:50,132 - INFO - 📋 共找到 3 个公众号，开始逐个处理...
2025-08-05 22:07:50,132 - INFO - ============================================================
2025-08-05 22:07:50,132 - INFO - 📍 处理第 1/3 个公众号: 钟山清风
2025-08-05 22:07:50,132 - INFO - ============================================================
2025-08-05 22:07:50,132 - INFO - [步骤 1/5] 为 '钟山清风' 创建独立的 Cookie 抓取器...
2025-08-05 22:07:50,132 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-05 22:07:50,132 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-05 22:07:50,132 - INFO - 步骤1: 正在准备网络环境...
2025-08-05 22:07:50,133 - INFO - === 开始重置网络状态 ===
2025-08-05 22:07:50,133 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-05 22:07:50,227 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-05 22:07:50,227 - INFO - 🔧 正在关闭系统代理设置...
2025-08-05 22:07:50,227 - INFO - 系统代理已成功关闭
2025-08-05 22:07:50,227 - INFO - ✅ 代理关闭操作
2025-08-05 22:07:50,227 - INFO - 🔗 正在验证网络连接...
2025-08-05 22:07:50,336 - INFO - ✅ 网络连接正常（无代理）- 测试网站: https://www.baidu.com
2025-08-05 22:07:50,337 - INFO - ✅ 网络状态重置验证完成
2025-08-05 22:07:50,337 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-05 22:07:50,337 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-05 22:07:50,338 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider_gitlab\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-05 22:07:51,968 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-05 22:07:51,969 - INFO - 🔄 Cookie抓取器进程已启动，PID: 31624
2025-08-05 22:07:51,970 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-05 22:07:54,970 - INFO - 等待代理服务启动...
2025-08-05 22:07:54,972 - INFO - ✅ 端口 8080 已开始监听
2025-08-05 22:07:55,060 - INFO - ✅ 代理服务已启动并正常工作
2025-08-05 22:07:55,060 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 31624)
2025-08-05 22:07:55,060 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-05 22:07:55,060 - INFO - [步骤 2/5] 为 '钟山清风' 启动 UI 自动化...
2025-08-05 22:07:55,061 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-05 22:07:55,061 - INFO - 准备将链接发送到文件传输助手...
2025-08-05 22:07:55,061 - INFO - 正在查找微信主窗口...
2025-08-05 22:08:06,012 - WARNING - 未找到 'WeChatMainWndForPC' 窗口，尝试备用方案...
2025-08-05 22:08:11,617 - ERROR - 未找到微信主窗口，请确保微信已登录并显示主界面。
2025-08-05 22:08:11,617 - ERROR - 发送链接失败，流程中止。
2025-08-05 22:08:11,617 - ERROR - ❌ 公众号 '钟山清风' UI 自动化触发失败，跳过此公众号
2025-08-05 22:08:11,617 - INFO - 🧹 开始清理抓取器资源...
2025-08-05 22:08:11,617 - INFO - 正在停止Cookie抓取器 (PID: 31624)...
2025-08-05 22:08:11,618 - INFO - Cookie抓取器已成功终止。
2025-08-05 22:08:11,618 - INFO - 正在验证并清理代理设置...
2025-08-05 22:08:11,618 - INFO - === 开始重置网络状态 ===
2025-08-05 22:08:11,618 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-05 22:08:11,710 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-05 22:08:11,710 - INFO - 🔧 正在关闭系统代理设置...
2025-08-05 22:08:11,710 - INFO - 系统代理已成功关闭
2025-08-05 22:08:11,710 - INFO - ✅ 代理关闭操作
2025-08-05 22:08:11,710 - INFO - 🔗 正在验证网络连接...
2025-08-05 22:08:12,816 - INFO - ✅ 网络连接正常（无代理）- 测试网站: https://www.baidu.com
2025-08-05 22:08:12,816 - INFO - ✅ 网络状态重置验证完成
2025-08-05 22:08:12,817 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-05 22:08:13,971 - INFO - ✅ 网络连接正常（无代理）- 测试网站: https://www.baidu.com
2025-08-05 22:08:13,972 - INFO - ✅ 网络连接验证正常
2025-08-05 22:08:13,973 - INFO - ============================================================
2025-08-05 22:08:13,974 - INFO - 📍 处理第 2/3 个公众号: 南京党建
2025-08-05 22:08:13,974 - INFO - ============================================================
2025-08-05 22:08:13,974 - INFO - [步骤 1/5] 为 '南京党建' 创建独立的 Cookie 抓取器...
2025-08-05 22:08:13,975 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-05 22:08:13,976 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-05 22:08:13,976 - INFO - 步骤1: 正在准备网络环境...
2025-08-05 22:08:13,977 - INFO - === 开始重置网络状态 ===
2025-08-05 22:08:13,977 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-05 22:08:14,105 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-05 22:08:14,105 - INFO - 🔧 正在关闭系统代理设置...
2025-08-05 22:08:14,105 - INFO - 系统代理已成功关闭
2025-08-05 22:08:14,105 - INFO - ✅ 代理关闭操作
2025-08-05 22:08:14,105 - INFO - 🔗 正在验证网络连接...
2025-08-05 22:08:14,202 - INFO - ✅ 网络连接正常（无代理）- 测试网站: https://www.baidu.com
2025-08-05 22:08:14,203 - INFO - ✅ 网络状态重置验证完成
2025-08-05 22:08:14,203 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-05 22:08:14,204 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-05 22:08:14,204 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider_gitlab\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-05 22:08:16,372 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-05 22:08:16,373 - INFO - 🔄 Cookie抓取器进程已启动，PID: 33784
2025-08-05 22:08:16,373 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-05 22:08:19,374 - INFO - 等待代理服务启动...
2025-08-05 22:08:19,374 - INFO - ✅ 端口 8080 已开始监听
2025-08-05 22:08:19,422 - INFO - ✅ 代理服务已启动并正常工作
2025-08-05 22:08:19,422 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 33784)
2025-08-05 22:08:19,422 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-05 22:08:19,422 - INFO - [步骤 2/5] 为 '南京党建' 启动 UI 自动化...
2025-08-05 22:08:19,422 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-05 22:08:19,423 - INFO - 准备将链接发送到文件传输助手...
2025-08-05 22:08:19,423 - INFO - 正在查找微信主窗口...
2025-08-05 22:08:26,526 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-05 22:08:26,526 - INFO - 正在激活微信窗口...
2025-08-05 22:08:29,038 - INFO - 微信窗口已激活。
2025-08-05 22:08:29,039 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-05 22:08:35,686 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-05 22:08:35,687 - INFO - 清空搜索框并将焦点转移到聊天区域...
