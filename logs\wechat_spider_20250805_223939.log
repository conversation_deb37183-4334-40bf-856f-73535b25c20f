2025-08-05 22:39:39,006 - INFO - ================================================================================
2025-08-05 22:39:39,006 - INFO - 🚀 微信公众号全自动爬取流程启动 🚀
2025-08-05 22:39:39,006 - INFO - ================================================================================
2025-08-05 22:39:39,006 - INFO - 版本: v3.0 - 全自动化版本
2025-08-05 22:39:39,006 - INFO - 设计用途: Windows任务计划程序自动执行
2025-08-05 22:39:39,007 - INFO - 执行时间: 2025-08-05 22:39:39
2025-08-05 22:39:39,007 - INFO - ================================================================================
2025-08-05 22:39:39,007 - INFO - 🔍 测试数据库连接...
2025-08-05 22:39:39,015 - INFO - ✅ 数据库连接成功: 127.0.0.1:3306/xuanfa
2025-08-05 22:39:39,017 - INFO - ✅ 数据库连接成功！当前有 64 篇文章
2025-08-05 22:39:39,017 - INFO - 💾 将启用数据库实时保存功能
2025-08-05 22:39:39,017 - INFO - 数据库连接已关闭
2025-08-05 22:39:39,017 - INFO - 启动全新自动化爬取流程...
2025-08-05 22:39:39,018 - INFO - ✅ 数据库连接成功: 127.0.0.1:3306/xuanfa
2025-08-05 22:39:39,019 - INFO - ✅ 数据库连接成功！当前有 64 篇文章
2025-08-05 22:39:39,019 - INFO - 数据库连接已关闭
2025-08-05 22:39:39,019 - INFO - ================================================================================
2025-08-05 22:39:39,019 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-05 22:39:39,019 - INFO - ================================================================================
2025-08-05 22:39:39,019 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-05 22:39:39,146 - INFO - 找到有效目标 1: 钟山清风 - https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&...
2025-08-05 22:39:39,146 - INFO - 找到有效目标 2: 南京党建 - http://mp.weixin.qq.com/s?__biz=MzI2OTMwNzU5Nw==&m...
2025-08-05 22:39:39,147 - INFO - 找到有效目标 3: 南京发布 - http://mp.weixin.qq.com/s?__biz=MjM5MTczODg0MA==&m...
2025-08-05 22:39:39,147 - INFO - 共找到 3 个有效的公众号目标
2025-08-05 22:39:39,147 - INFO - 📋 共找到 3 个公众号，开始逐个处理...
2025-08-05 22:39:39,147 - INFO - ============================================================
2025-08-05 22:39:39,147 - INFO - 📍 处理第 1/3 个公众号: 钟山清风
2025-08-05 22:39:39,148 - INFO - ============================================================
2025-08-05 22:39:39,148 - INFO - [步骤 1/5] 为 '钟山清风' 创建独立的 Cookie 抓取器...
2025-08-05 22:39:39,148 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-05 22:39:39,148 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-05 22:39:39,148 - INFO - 步骤1: 正在准备网络环境...
2025-08-05 22:39:39,148 - INFO - === 开始重置网络状态 ===
2025-08-05 22:39:39,149 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-05 22:39:39,237 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-05 22:39:39,237 - INFO - 🔧 正在关闭系统代理设置...
2025-08-05 22:39:39,237 - INFO - 系统代理已成功关闭
2025-08-05 22:39:39,237 - INFO - ✅ 代理关闭操作
2025-08-05 22:39:39,237 - INFO - 🔗 正在验证网络连接...
2025-08-05 22:39:39,349 - INFO - ✅ 网络连接正常（无代理）- 测试网站: https://www.baidu.com
2025-08-05 22:39:39,349 - INFO - ✅ 网络状态重置验证完成
2025-08-05 22:39:39,350 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-05 22:39:39,350 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-05 22:39:39,350 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider_gitlab\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-05 22:39:39,993 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-05 22:39:39,993 - INFO - 🔄 Cookie抓取器进程已启动，PID: 28180
2025-08-05 22:39:39,994 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-05 22:39:42,994 - INFO - 等待代理服务启动...
2025-08-05 22:39:42,995 - INFO - ✅ 端口 8080 已开始监听
2025-08-05 22:39:43,039 - INFO - ✅ 代理服务已启动并正常工作
2025-08-05 22:39:43,040 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 28180)
2025-08-05 22:39:43,040 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-05 22:39:43,040 - INFO - [步骤 2/5] 为 '钟山清风' 启动 UI 自动化...
2025-08-05 22:39:43,041 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-05 22:39:43,041 - INFO - 准备将链接发送到文件传输助手...
2025-08-05 22:39:43,042 - INFO - 正在查找微信主窗口...
2025-08-05 22:39:43,119 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-05 22:39:43,119 - INFO - 正在激活微信窗口...
2025-08-05 22:39:45,636 - INFO - 微信窗口已激活。
2025-08-05 22:39:45,636 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-05 22:39:52,347 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-05 22:39:52,348 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-05 22:39:55,682 - INFO - 正在查找聊天输入框...
2025-08-05 22:39:57,683 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-05 22:39:57,695 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-05 22:39:57,696 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-05 22:39:59,270 - INFO - 已将链接复制到剪贴板: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&mid=2247521212&idx=1&sn=2d7cae536e0ced5e4f59ded16b88ab30&chksm=cf77a61131b97056fa39d8d9863d17ebd6c23a14e37c3dfd8cdcc5bb00fe2c549e215b8928d8&scene=27#wechat_redirect
2025-08-05 22:40:01,556 - INFO - 链接已粘贴，正在发送...
2025-08-05 22:40:01,802 - INFO - 找到发送按钮，点击发送...
2025-08-05 22:40:02,617 - INFO - 链接已发送。
2025-08-05 22:40:05,618 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-05 22:40:07,705 - INFO - 已定位到最新的消息项，准备点击。
2025-08-05 22:40:08,432 - INFO - ✅ 成功点击最新链接。
2025-08-05 22:40:11,433 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-05 22:40:11,433 - INFO - 正在查找微信浏览器窗口...
2025-08-05 22:40:11,433 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-05 22:40:11,440 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-05 22:40:14,148 - INFO - 已成功激活焦点浏览器窗口
2025-08-05 22:40:15,649 - INFO - 正在检测SSL证书错误页面...
2025-08-05 22:40:28,010 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-05 22:40:28,010 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-05 22:40:28,010 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-05 22:40:28,010 - INFO - 正在执行第 1 次刷新操作...
2025-08-05 22:40:28,011 - INFO - 从文件中解析到有效Cookie数据。
2025-08-05 22:40:28,011 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-05 22:40:28,011 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-05 22:40:28,011 - INFO - 从文件中解析到有效Cookie数据。
2025-08-05 22:40:28,011 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-05 22:40:28,011 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-05 22:40:28,012 - INFO - [步骤 3/5] 等待 '钟山清风' 的 Cookie 数据...
2025-08-05 22:40:28,012 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-05 22:40:29,012 - INFO - 检测到Cookie文件已生成。
2025-08-05 22:40:29,013 - INFO - 从文件中解析到有效Cookie数据。
2025-08-05 22:40:29,014 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-05 22:40:29,014 - INFO - [步骤 4/5] 停止 '钟山清风' 的 Cookie 抓取器...
2025-08-05 22:40:29,014 - INFO - 🧹 开始清理抓取器资源...
2025-08-05 22:40:29,015 - INFO - 正在停止Cookie抓取器 (PID: 28180)...
2025-08-05 22:40:29,017 - INFO - Cookie抓取器已成功终止。
2025-08-05 22:40:29,017 - INFO - 正在验证并清理代理设置...
2025-08-05 22:40:29,018 - INFO - === 开始重置网络状态 ===
2025-08-05 22:40:29,018 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-05 22:40:29,143 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-05 22:40:29,144 - INFO - 🔧 正在关闭系统代理设置...
2025-08-05 22:40:29,144 - INFO - 系统代理已成功关闭
2025-08-05 22:40:29,144 - INFO - ✅ 代理关闭操作
2025-08-05 22:40:29,144 - INFO - 🔗 正在验证网络连接...
2025-08-05 22:40:29,172 - INFO - ✅ 网络连接正常（无代理）- 测试网站: https://www.baidu.com
2025-08-05 22:40:29,172 - INFO - ✅ 网络状态重置验证完成
2025-08-05 22:40:29,172 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-05 22:40:29,220 - INFO - ✅ 网络连接正常（无代理）- 测试网站: https://www.baidu.com
2025-08-05 22:40:29,221 - INFO - ✅ 网络连接验证正常
2025-08-05 22:40:32,222 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-05 22:40:32,223 - INFO - [步骤 5/5] 开始爬取 '钟山清风' 的文章...
2025-08-05 22:40:32,223 - INFO - 🔄 第 1/2 次尝试爬取...
2025-08-05 22:40:32,240 - INFO - ✅ 数据库连接成功: 127.0.0.1:3306/xuanfa
2025-08-05 22:40:32,508 - INFO - ✅ Cookie验证成功，开始正式爬取...
2025-08-05 22:40:41,852 - INFO - ✅ 文章插入成功: 六合区纪委监委：督促完善供水设施   化解农村用水难题 (ID: 2025080522402391)
2025-08-05 22:40:54,660 - INFO - ✅ 文章插入成功: 节礼去奢入俭   回归节日本味 (ID: 2025080522400350)
2025-08-05 22:41:07,501 - INFO - ✅ 文章插入成功: 雨花台区纪委监委：精准监督 推动兜牢民生底线 (ID: 2025080522413495)
2025-08-05 22:41:19,560 - INFO - ✅ 文章插入成功: 让担当作为蔚然成风 (ID: 2025080522415999)
