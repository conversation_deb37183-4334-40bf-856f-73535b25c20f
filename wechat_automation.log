2025-08-05 21:36:47,520 - INFO - Imported existing <module 'comtypes.gen' from 'D:\\mynj\\mynj_env\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-05 21:36:47,527 - INFO - Using writeable comtypes cache directory: 'D:\mynj\mynj_env\Lib\site-packages\comtypes\gen'
2025-08-05 21:36:47,533 - INFO - ================================================================================
2025-08-05 21:36:47,533 - INFO - 🚀 微信公众号全自动爬取流程启动 🚀
2025-08-05 21:36:47,533 - INFO - ================================================================================
2025-08-05 21:36:47,533 - INFO - 版本: v3.0 - 全自动化版本
2025-08-05 21:36:47,534 - INFO - 设计用途: Windows任务计划程序自动执行
2025-08-05 21:36:47,534 - INFO - 执行时间: 2025-08-05 21:36:47
2025-08-05 21:36:47,534 - INFO - ================================================================================
2025-08-05 21:36:47,534 - INFO - 启动全新自动化爬取流程...
2025-08-05 21:36:47,534 - INFO - ================================================================================
2025-08-05 21:36:47,534 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-05 21:36:47,534 - INFO - ================================================================================
2025-08-05 21:36:47,534 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-05 21:36:48,077 - INFO - 找到有效目标 1: 钟山清风 - https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&...
2025-08-05 21:36:48,078 - INFO - 找到有效目标 2: 南京党建 - http://mp.weixin.qq.com/s?__biz=MzI2OTMwNzU5Nw==&m...
2025-08-05 21:36:48,078 - INFO - 找到有效目标 3: 南京发布 - http://mp.weixin.qq.com/s?__biz=MjM5MTczODg0MA==&m...
2025-08-05 21:36:48,078 - INFO - 共找到 3 个有效的公众号目标
2025-08-05 21:36:48,078 - INFO - 📋 共找到 3 个公众号，开始逐个处理...
2025-08-05 21:36:48,079 - INFO - ============================================================
2025-08-05 21:36:48,079 - INFO - 📍 处理第 1/3 个公众号: 钟山清风
2025-08-05 21:36:48,079 - INFO - ============================================================
2025-08-05 21:36:48,079 - INFO - [步骤 1/5] 为 '钟山清风' 创建独立的 Cookie 抓取器...
2025-08-05 21:36:48,080 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-05 21:36:48,080 - INFO - 步骤1: 正在准备网络环境...
2025-08-05 21:36:48,080 - INFO - === 开始重置网络状态 ===
2025-08-05 21:36:48,080 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-05 21:36:48,178 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-05 21:36:48,179 - INFO - 🔧 正在关闭系统代理设置...
2025-08-05 21:36:48,179 - INFO - 系统代理已成功关闭
2025-08-05 21:36:48,179 - INFO - ✅ 代理关闭操作
2025-08-05 21:36:48,179 - INFO - 🔗 正在验证网络连接...
2025-08-05 21:36:48,380 - INFO - ✅ 网络连接正常（无代理）- 测试网站: https://www.baidu.com
2025-08-05 21:36:48,396 - INFO - ✅ 网络状态重置验证完成
2025-08-05 21:36:48,396 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-05 21:36:48,396 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-05 21:36:48,396 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider_gitlab\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-05 21:36:51,111 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-05 21:36:51,111 - INFO - 🔄 Cookie抓取器进程已启动，PID: 22624
2025-08-05 21:36:51,112 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-05 21:36:54,112 - INFO - 等待代理服务启动...
2025-08-05 21:36:54,114 - INFO - ✅ 端口 8080 已开始监听
2025-08-05 21:36:54,200 - INFO - ✅ 代理服务已启动并正常工作
2025-08-05 21:36:54,200 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 22624)
2025-08-05 21:36:54,201 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-05 21:36:54,201 - INFO - [步骤 2/5] 为 '钟山清风' 启动 UI 自动化...
2025-08-05 21:36:54,202 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-05 21:36:54,202 - INFO - 准备将链接发送到文件传输助手...
2025-08-05 21:36:54,202 - INFO - 正在查找微信主窗口...
2025-08-05 21:36:54,314 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-05 21:36:54,314 - INFO - 正在激活微信窗口...
2025-08-05 21:36:56,832 - INFO - 微信窗口已激活。
2025-08-05 21:36:56,833 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-05 21:37:03,421 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-05 21:37:03,422 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-05 21:37:06,752 - INFO - 正在查找聊天输入框...
2025-08-05 21:37:08,753 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-05 21:37:08,763 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-05 21:37:08,763 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-05 21:37:10,341 - INFO - 已将链接复制到剪贴板: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&mid=2247521212&idx=1&sn=2d7cae536e0ced5e4f59ded16b88ab30&chksm=cf77a61131b97056fa39d8d9863d17ebd6c23a14e37c3dfd8cdcc5bb00fe2c549e215b8928d8&scene=27#wechat_redirect
2025-08-05 21:37:12,626 - INFO - 链接已粘贴，正在发送...
2025-08-05 21:37:12,909 - INFO - 找到发送按钮，点击发送...
2025-08-05 21:37:13,721 - INFO - 链接已发送。
2025-08-05 21:37:16,723 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-05 21:37:18,808 - INFO - 已定位到最新的消息项，准备点击。
2025-08-05 21:37:19,538 - INFO - ✅ 成功点击最新链接。
2025-08-05 21:37:22,538 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-05 21:37:22,539 - INFO - 正在查找微信浏览器窗口...
2025-08-05 21:37:22,539 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-05 21:37:22,556 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-05 21:37:25,260 - INFO - 已成功激活焦点浏览器窗口
2025-08-05 21:37:26,761 - INFO - 正在检测SSL证书错误页面...
2025-08-05 21:37:40,038 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-05 21:37:40,038 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-05 21:37:40,038 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-05 21:37:40,038 - INFO - 正在执行第 1 次刷新操作...
2025-08-05 21:37:40,038 - WARNING - 在文件中未找到有效的Cookie数据。
2025-08-05 21:37:40,038 - INFO - 正在查找微信浏览器窗口...
2025-08-05 21:37:40,038 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-05 21:37:40,041 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-05 21:37:42,746 - INFO - 已成功激活焦点浏览器窗口
2025-08-05 21:37:43,247 - INFO - 正在查找微信浏览器窗口...
2025-08-05 21:37:43,248 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-05 21:37:43,257 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-05 21:37:45,963 - INFO - 已成功激活焦点浏览器窗口
2025-08-05 21:37:47,464 - INFO - 正在检测SSL证书错误页面...
2025-08-05 21:38:00,369 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-05 21:38:00,370 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-05 21:38:02,871 - INFO - 从文件中解析到有效Cookie数据。
2025-08-05 21:38:02,872 - INFO - 🎉 检测到抓包成功！在第 1 次刷新后停止
2025-08-05 21:38:02,872 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-05 21:38:02,873 - INFO - 从文件中解析到有效Cookie数据。
2025-08-05 21:38:02,873 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-05 21:38:02,874 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-05 21:38:02,875 - INFO - [步骤 3/5] 等待 '钟山清风' 的 Cookie 数据...
2025-08-05 21:38:02,875 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-05 21:38:03,876 - INFO - 检测到Cookie文件已生成。
2025-08-05 21:38:03,876 - INFO - 从文件中解析到有效Cookie数据。
2025-08-05 21:38:03,876 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-05 21:38:03,876 - INFO - [步骤 4/5] 停止 '钟山清风' 的 Cookie 抓取器...
2025-08-05 21:38:03,876 - INFO - 🧹 开始清理抓取器资源...
2025-08-05 21:38:03,877 - INFO - 正在停止Cookie抓取器 (PID: 22624)...
2025-08-05 21:38:03,878 - INFO - Cookie抓取器已成功终止。
2025-08-05 21:38:03,878 - INFO - 正在验证并清理代理设置...
2025-08-05 21:38:03,878 - INFO - === 开始重置网络状态 ===
2025-08-05 21:38:03,878 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-05 21:38:03,968 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-05 21:38:03,968 - INFO - 🔧 正在关闭系统代理设置...
2025-08-05 21:38:03,968 - INFO - 系统代理已成功关闭
2025-08-05 21:38:03,968 - INFO - ✅ 代理关闭操作
2025-08-05 21:38:03,969 - INFO - 🔗 正在验证网络连接...
2025-08-05 21:38:04,030 - INFO - ✅ 网络连接正常（无代理）- 测试网站: https://www.baidu.com
2025-08-05 21:38:04,030 - INFO - ✅ 网络状态重置验证完成
2025-08-05 21:38:04,030 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-05 21:38:04,112 - INFO - ✅ 网络连接正常（无代理）- 测试网站: https://www.baidu.com
2025-08-05 21:38:04,113 - INFO - ✅ 网络连接验证正常
2025-08-05 21:38:07,113 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-05 21:38:07,114 - INFO - [步骤 5/5] 开始爬取 '钟山清风' 的文章...
2025-08-05 21:38:07,114 - INFO - 🔄 第 1/2 次尝试爬取...
2025-08-05 21:38:07,381 - INFO - ✅ Cookie验证成功，开始正式爬取...
2025-08-05 21:42:23,928 - INFO - Imported existing <module 'comtypes.gen' from 'D:\\mynj\\mynj_env\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-05 21:42:23,929 - INFO - Using writeable comtypes cache directory: 'D:\mynj\mynj_env\Lib\site-packages\comtypes\gen'
2025-08-05 21:42:23,933 - INFO - ================================================================================
2025-08-05 21:42:23,934 - INFO - 🚀 微信公众号全自动爬取流程启动 🚀
2025-08-05 21:42:23,934 - INFO - ================================================================================
2025-08-05 21:42:23,934 - INFO - 版本: v3.0 - 全自动化版本
2025-08-05 21:42:23,934 - INFO - 设计用途: Windows任务计划程序自动执行
2025-08-05 21:42:23,934 - INFO - 执行时间: 2025-08-05 21:42:23
2025-08-05 21:42:23,934 - INFO - ================================================================================
2025-08-05 21:42:23,934 - INFO - 启动全新自动化爬取流程...
2025-08-05 21:42:23,934 - INFO - ================================================================================
2025-08-05 21:42:23,934 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-05 21:42:23,935 - INFO - ================================================================================
2025-08-05 21:42:23,935 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-05 21:42:24,164 - INFO - 找到有效目标 1: 钟山清风 - https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&...
2025-08-05 21:42:24,165 - INFO - 找到有效目标 2: 南京党建 - http://mp.weixin.qq.com/s?__biz=MzI2OTMwNzU5Nw==&m...
2025-08-05 21:42:24,165 - INFO - 找到有效目标 3: 南京发布 - http://mp.weixin.qq.com/s?__biz=MjM5MTczODg0MA==&m...
2025-08-05 21:42:24,165 - INFO - 共找到 3 个有效的公众号目标
2025-08-05 21:42:24,165 - INFO - 📋 共找到 3 个公众号，开始逐个处理...
2025-08-05 21:42:24,165 - INFO - ============================================================
2025-08-05 21:42:24,165 - INFO - 📍 处理第 1/3 个公众号: 钟山清风
2025-08-05 21:42:24,165 - INFO - ============================================================
2025-08-05 21:42:24,165 - INFO - [步骤 1/5] 为 '钟山清风' 创建独立的 Cookie 抓取器...
2025-08-05 21:42:24,165 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-05 21:42:24,166 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-05 21:42:24,166 - INFO - 步骤1: 正在准备网络环境...
2025-08-05 21:42:24,166 - INFO - === 开始重置网络状态 ===
2025-08-05 21:42:24,166 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-05 21:42:24,260 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-05 21:42:24,260 - INFO - 🔧 正在关闭系统代理设置...
2025-08-05 21:42:24,260 - INFO - 系统代理已成功关闭
2025-08-05 21:42:24,260 - INFO - ✅ 代理关闭操作
2025-08-05 21:42:24,260 - INFO - 🔗 正在验证网络连接...
2025-08-05 21:42:24,354 - INFO - ✅ 网络连接正常（无代理）- 测试网站: https://www.baidu.com
2025-08-05 21:42:24,355 - INFO - ✅ 网络状态重置验证完成
2025-08-05 21:42:24,355 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-05 21:42:24,355 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-05 21:42:24,356 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider_gitlab\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-05 21:42:26,036 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-05 21:42:26,036 - INFO - 🔄 Cookie抓取器进程已启动，PID: 16812
2025-08-05 21:42:26,037 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-05 21:42:29,037 - INFO - 等待代理服务启动...
2025-08-05 21:42:29,038 - INFO - ✅ 端口 8080 已开始监听
2025-08-05 21:42:29,059 - INFO - ✅ 代理服务已启动并正常工作
2025-08-05 21:42:29,059 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 16812)
2025-08-05 21:42:29,059 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-05 21:42:29,059 - INFO - [步骤 2/5] 为 '钟山清风' 启动 UI 自动化...
2025-08-05 21:42:29,060 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-05 21:42:29,060 - INFO - 准备将链接发送到文件传输助手...
2025-08-05 21:42:29,060 - INFO - 正在查找微信主窗口...
2025-08-05 21:42:30,215 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-05 21:42:30,216 - INFO - 正在激活微信窗口...
2025-08-05 21:42:32,750 - INFO - 微信窗口已激活。
2025-08-05 21:42:32,751 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-05 21:42:39,435 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-05 21:42:39,436 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-05 21:42:42,768 - INFO - 正在查找聊天输入框...
2025-08-05 21:42:44,770 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-05 21:42:44,778 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-05 21:42:44,778 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-05 21:42:46,370 - INFO - 已将链接复制到剪贴板: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&mid=2247521212&idx=1&sn=2d7cae536e0ced5e4f59ded16b88ab30&chksm=cf77a61131b97056fa39d8d9863d17ebd6c23a14e37c3dfd8cdcc5bb00fe2c549e215b8928d8&scene=27#wechat_redirect
2025-08-05 21:42:48,655 - INFO - 链接已粘贴，正在发送...
2025-08-05 21:42:48,900 - INFO - 找到发送按钮，点击发送...
2025-08-05 21:42:49,701 - INFO - 链接已发送。
2025-08-05 21:42:52,702 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-05 21:42:54,796 - INFO - 已定位到最新的消息项，准备点击。
2025-08-05 21:42:55,521 - INFO - ✅ 成功点击最新链接。
2025-08-05 21:42:58,521 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-05 21:42:58,522 - INFO - 正在查找微信浏览器窗口...
2025-08-05 21:42:58,522 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-05 21:42:58,530 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-05 21:43:01,237 - INFO - 已成功激活焦点浏览器窗口
2025-08-05 21:43:02,738 - INFO - 正在检测SSL证书错误页面...
2025-08-05 21:43:14,996 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-05 21:43:14,996 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-05 21:43:14,996 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-05 21:43:14,996 - INFO - 正在执行第 1 次刷新操作...
2025-08-05 21:43:14,996 - INFO - 从文件中解析到有效Cookie数据。
2025-08-05 21:43:14,996 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-05 21:43:14,996 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-05 21:43:14,997 - INFO - 从文件中解析到有效Cookie数据。
2025-08-05 21:43:14,997 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-05 21:43:14,997 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-05 21:43:14,997 - INFO - [步骤 3/5] 等待 '钟山清风' 的 Cookie 数据...
2025-08-05 21:43:14,997 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-05 21:43:15,997 - INFO - 检测到Cookie文件已生成。
2025-08-05 21:43:15,998 - INFO - 从文件中解析到有效Cookie数据。
2025-08-05 21:43:15,999 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-05 21:43:15,999 - INFO - [步骤 4/5] 停止 '钟山清风' 的 Cookie 抓取器...
2025-08-05 21:43:16,000 - INFO - 🧹 开始清理抓取器资源...
2025-08-05 21:43:16,000 - INFO - 正在停止Cookie抓取器 (PID: 16812)...
2025-08-05 21:43:16,002 - INFO - Cookie抓取器已成功终止。
2025-08-05 21:43:16,002 - INFO - 正在验证并清理代理设置...
2025-08-05 21:43:16,002 - INFO - === 开始重置网络状态 ===
2025-08-05 21:43:16,003 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-05 21:43:16,130 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-05 21:43:16,130 - INFO - 🔧 正在关闭系统代理设置...
2025-08-05 21:43:16,131 - INFO - 系统代理已成功关闭
2025-08-05 21:43:16,131 - INFO - ✅ 代理关闭操作
2025-08-05 21:43:16,131 - INFO - 🔗 正在验证网络连接...
2025-08-05 21:43:16,220 - INFO - ✅ 网络连接正常（无代理）- 测试网站: https://www.baidu.com
2025-08-05 21:43:16,221 - INFO - ✅ 网络状态重置验证完成
2025-08-05 21:43:16,221 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-05 21:43:16,320 - INFO - ✅ 网络连接正常（无代理）- 测试网站: https://www.baidu.com
2025-08-05 21:43:16,321 - INFO - ✅ 网络连接验证正常
2025-08-05 21:43:19,321 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-05 21:43:19,322 - INFO - [步骤 5/5] 开始爬取 '钟山清风' 的文章...
2025-08-05 21:43:19,323 - INFO - 🔄 第 1/2 次尝试爬取...
2025-08-05 21:43:19,585 - INFO - ✅ Cookie验证成功，开始正式爬取...
2025-08-05 21:44:45,725 - INFO - ✅ 公众号 '钟山清风' 爬取完成！获取 4 篇文章
2025-08-05 21:44:45,725 - INFO - 📊 数据已保存到: ./data/readnum_batch/readnum_钟山清风_20250805_214445.xlsx
2025-08-05 21:44:45,725 - INFO - ⏳ 公众号间延迟 15 秒...
2025-08-05 21:45:00,726 - INFO - ============================================================
2025-08-05 21:45:00,726 - INFO - 📍 处理第 2/3 个公众号: 南京党建
2025-08-05 21:45:00,727 - INFO - ============================================================
2025-08-05 21:45:00,727 - INFO - [步骤 1/5] 为 '南京党建' 创建独立的 Cookie 抓取器...
2025-08-05 21:45:00,728 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-05 21:45:00,728 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-05 21:45:00,728 - INFO - 步骤1: 正在准备网络环境...
2025-08-05 21:45:00,729 - INFO - === 开始重置网络状态 ===
2025-08-05 21:45:00,729 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-05 21:45:00,855 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-05 21:45:00,855 - INFO - 🔧 正在关闭系统代理设置...
2025-08-05 21:45:00,855 - INFO - 系统代理已成功关闭
2025-08-05 21:45:00,855 - INFO - ✅ 代理关闭操作
2025-08-05 21:45:00,855 - INFO - 🔗 正在验证网络连接...
2025-08-05 21:45:00,935 - INFO - ✅ 网络连接正常（无代理）- 测试网站: https://www.baidu.com
2025-08-05 21:45:00,936 - INFO - ✅ 网络状态重置验证完成
2025-08-05 21:45:00,936 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-05 21:45:00,936 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-05 21:45:00,937 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider_gitlab\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-05 21:45:01,484 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-05 21:45:01,485 - INFO - 🔄 Cookie抓取器进程已启动，PID: 23496
2025-08-05 21:45:01,485 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-05 21:45:04,486 - INFO - 等待代理服务启动...
2025-08-05 21:45:04,487 - INFO - ✅ 端口 8080 已开始监听
2025-08-05 21:45:04,584 - INFO - ✅ 代理服务已启动并正常工作
2025-08-05 21:45:04,585 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 23496)
2025-08-05 21:45:04,585 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-05 21:45:04,585 - INFO - [步骤 2/5] 为 '南京党建' 启动 UI 自动化...
2025-08-05 21:45:04,586 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-05 21:45:04,586 - INFO - 准备将链接发送到文件传输助手...
2025-08-05 21:45:04,587 - INFO - 正在查找微信主窗口...
2025-08-05 21:45:05,405 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-05 21:45:05,405 - INFO - 正在激活微信窗口...
2025-08-05 21:45:07,919 - INFO - 微信窗口已激活。
2025-08-05 21:45:07,920 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-05 21:51:11,876 - INFO - Imported existing <module 'comtypes.gen' from 'D:\\mynj\\mynj_env\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-05 21:51:11,876 - INFO - Using writeable comtypes cache directory: 'D:\mynj\mynj_env\Lib\site-packages\comtypes\gen'
2025-08-05 21:51:11,881 - INFO - ================================================================================
2025-08-05 21:51:11,881 - INFO - 🚀 微信公众号全自动爬取流程启动 🚀
2025-08-05 21:51:11,881 - INFO - ================================================================================
2025-08-05 21:51:11,881 - INFO - 版本: v3.0 - 全自动化版本
2025-08-05 21:51:11,881 - INFO - 设计用途: Windows任务计划程序自动执行
2025-08-05 21:51:11,881 - INFO - 执行时间: 2025-08-05 21:51:11
2025-08-05 21:51:11,881 - INFO - ================================================================================
2025-08-05 21:51:11,882 - INFO - 启动全新自动化爬取流程...
2025-08-05 21:51:11,882 - INFO - ================================================================================
2025-08-05 21:51:11,882 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-05 21:51:11,882 - INFO - ================================================================================
2025-08-05 21:51:11,882 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-05 21:51:11,951 - INFO - 找到有效目标 1: 钟山清风 - https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&...
2025-08-05 21:51:11,952 - INFO - 找到有效目标 2: 南京党建 - http://mp.weixin.qq.com/s?__biz=MzI2OTMwNzU5Nw==&m...
2025-08-05 21:51:11,952 - INFO - 找到有效目标 3: 南京发布 - http://mp.weixin.qq.com/s?__biz=MjM5MTczODg0MA==&m...
2025-08-05 21:51:11,952 - INFO - 共找到 3 个有效的公众号目标
2025-08-05 21:51:11,952 - INFO - 📋 共找到 3 个公众号，开始逐个处理...
2025-08-05 21:51:11,952 - INFO - ============================================================
2025-08-05 21:51:11,952 - INFO - 📍 处理第 1/3 个公众号: 钟山清风
2025-08-05 21:51:11,952 - INFO - ============================================================
2025-08-05 21:51:11,952 - INFO - [步骤 1/5] 为 '钟山清风' 创建独立的 Cookie 抓取器...
2025-08-05 21:51:11,952 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-05 21:51:11,952 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-05 21:51:11,952 - INFO - 步骤1: 正在准备网络环境...
2025-08-05 21:51:11,952 - INFO - === 开始重置网络状态 ===
2025-08-05 21:51:11,953 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-05 21:51:12,063 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-05 21:51:12,063 - INFO - 🔧 正在关闭系统代理设置...
2025-08-05 21:51:12,063 - INFO - 系统代理已成功关闭
2025-08-05 21:51:12,063 - INFO - ✅ 代理关闭操作
2025-08-05 21:51:12,064 - INFO - 🔗 正在验证网络连接...
2025-08-05 21:51:12,198 - INFO - ✅ 网络连接正常（无代理）- 测试网站: https://www.baidu.com
2025-08-05 21:51:12,200 - INFO - ✅ 网络状态重置验证完成
2025-08-05 21:51:12,200 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-05 21:51:12,200 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-05 21:51:12,201 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider_gitlab\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-05 21:51:12,710 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-05 21:51:12,711 - INFO - 🔄 Cookie抓取器进程已启动，PID: 23196
2025-08-05 21:51:12,711 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-05 21:51:15,711 - INFO - 等待代理服务启动...
2025-08-05 21:51:15,713 - INFO - ✅ 端口 8080 已开始监听
2025-08-05 21:51:15,806 - INFO - ✅ 代理服务已启动并正常工作
2025-08-05 21:51:15,807 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 23196)
2025-08-05 21:51:15,807 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-05 21:51:15,808 - INFO - [步骤 2/5] 为 '钟山清风' 启动 UI 自动化...
2025-08-05 21:51:15,808 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-05 21:51:15,809 - INFO - 准备将链接发送到文件传输助手...
2025-08-05 21:51:15,809 - INFO - 正在查找微信主窗口...
2025-08-05 21:51:17,112 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-05 21:51:17,113 - INFO - 正在激活微信窗口...
2025-08-05 21:51:19,637 - INFO - 微信窗口已激活。
2025-08-05 21:51:19,637 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-05 21:51:26,302 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-05 21:51:26,303 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-05 21:51:29,636 - INFO - 正在查找聊天输入框...
2025-08-05 21:51:31,637 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-05 21:51:31,640 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-05 21:51:31,640 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-05 21:51:33,227 - INFO - 已将链接复制到剪贴板: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&mid=2247521212&idx=1&sn=2d7cae536e0ced5e4f59ded16b88ab30&chksm=cf77a61131b97056fa39d8d9863d17ebd6c23a14e37c3dfd8cdcc5bb00fe2c549e215b8928d8&scene=27#wechat_redirect
2025-08-05 21:51:35,515 - INFO - 链接已粘贴，正在发送...
2025-08-05 21:51:35,760 - INFO - 找到发送按钮，点击发送...
2025-08-05 21:51:36,571 - INFO - 链接已发送。
2025-08-05 21:51:39,572 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-05 21:51:41,662 - INFO - 已定位到最新的消息项，准备点击。
2025-08-05 21:51:42,387 - INFO - ✅ 成功点击最新链接。
2025-08-05 21:51:45,388 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-05 21:51:45,388 - INFO - 正在查找微信浏览器窗口...
2025-08-05 21:51:45,388 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-05 21:51:45,392 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-05 21:51:48,098 - INFO - 已成功激活焦点浏览器窗口
2025-08-05 21:51:49,599 - INFO - 正在检测SSL证书错误页面...
2025-08-05 21:52:01,850 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-05 21:52:01,850 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-05 21:52:01,850 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-05 21:52:01,850 - INFO - 正在执行第 1 次刷新操作...
2025-08-05 21:52:01,851 - INFO - 从文件中解析到有效Cookie数据。
2025-08-05 21:52:01,851 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-05 21:52:01,851 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-05 21:52:01,851 - INFO - 从文件中解析到有效Cookie数据。
2025-08-05 21:52:01,851 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-05 21:52:01,851 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-05 21:52:01,851 - INFO - [步骤 3/5] 等待 '钟山清风' 的 Cookie 数据...
2025-08-05 21:52:01,852 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-05 21:52:02,852 - INFO - 检测到Cookie文件已生成。
2025-08-05 21:52:02,853 - INFO - 从文件中解析到有效Cookie数据。
2025-08-05 21:52:02,853 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-05 21:52:02,854 - INFO - [步骤 4/5] 停止 '钟山清风' 的 Cookie 抓取器...
2025-08-05 21:52:02,854 - INFO - 🧹 开始清理抓取器资源...
2025-08-05 21:52:02,854 - INFO - 正在停止Cookie抓取器 (PID: 23196)...
2025-08-05 21:52:02,856 - INFO - Cookie抓取器已成功终止。
2025-08-05 21:52:02,856 - INFO - 正在验证并清理代理设置...
2025-08-05 21:52:02,857 - INFO - === 开始重置网络状态 ===
2025-08-05 21:52:02,857 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-05 21:52:02,952 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-05 21:52:02,952 - INFO - 🔧 正在关闭系统代理设置...
2025-08-05 21:52:02,952 - INFO - 系统代理已成功关闭
2025-08-05 21:52:02,953 - INFO - ✅ 代理关闭操作
2025-08-05 21:52:02,953 - INFO - 🔗 正在验证网络连接...
2025-08-05 21:52:03,001 - INFO - ✅ 网络连接正常（无代理）- 测试网站: https://www.baidu.com
2025-08-05 21:52:03,002 - INFO - ✅ 网络状态重置验证完成
2025-08-05 21:52:03,002 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-05 21:52:03,089 - INFO - ✅ 网络连接正常（无代理）- 测试网站: https://www.baidu.com
2025-08-05 21:52:03,090 - INFO - ✅ 网络连接验证正常
2025-08-05 21:52:06,091 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-05 21:52:06,092 - INFO - [步骤 5/5] 开始爬取 '钟山清风' 的文章...
2025-08-05 21:52:06,092 - INFO - 🔄 第 1/2 次尝试爬取...
2025-08-05 21:52:06,422 - INFO - ✅ Cookie验证成功，开始正式爬取...
2025-08-05 21:53:23,952 - INFO - ✅ 公众号 '钟山清风' 爬取完成！获取 4 篇文章
2025-08-05 21:53:23,952 - INFO - 📊 数据已保存到: ./data/readnum_batch/readnum_钟山清风_20250805_215323.xlsx
2025-08-05 21:53:23,952 - INFO - ⏳ 公众号间延迟 15 秒...
2025-08-05 21:53:38,953 - INFO - ============================================================
2025-08-05 21:53:38,953 - INFO - 📍 处理第 2/3 个公众号: 南京党建
2025-08-05 21:53:38,953 - INFO - ============================================================
2025-08-05 21:53:38,953 - INFO - [步骤 1/5] 为 '南京党建' 创建独立的 Cookie 抓取器...
2025-08-05 21:53:38,954 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-05 21:53:38,954 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-05 21:53:38,954 - INFO - 步骤1: 正在准备网络环境...
2025-08-05 21:53:38,954 - INFO - === 开始重置网络状态 ===
2025-08-05 21:53:38,954 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-05 21:53:39,040 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-05 21:53:39,040 - INFO - 🔧 正在关闭系统代理设置...
2025-08-05 21:53:39,041 - INFO - 系统代理已成功关闭
2025-08-05 21:53:39,041 - INFO - ✅ 代理关闭操作
2025-08-05 21:53:39,041 - INFO - 🔗 正在验证网络连接...
2025-08-05 21:53:40,108 - INFO - ✅ 网络连接正常（无代理）- 测试网站: https://www.baidu.com
2025-08-05 21:53:40,108 - INFO - ✅ 网络状态重置验证完成
2025-08-05 21:53:40,108 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-05 21:53:40,108 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-05 21:53:40,109 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider_gitlab\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-05 21:53:42,596 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-05 21:53:42,597 - INFO - 🔄 Cookie抓取器进程已启动，PID: 31572
2025-08-05 21:53:42,597 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-05 21:53:45,597 - INFO - 等待代理服务启动...
2025-08-05 21:53:45,598 - INFO - ✅ 端口 8080 已开始监听
2025-08-05 21:53:48,941 - INFO - ✅ 代理服务已启动并正常工作
2025-08-05 21:53:48,942 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 31572)
2025-08-05 21:53:48,942 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-05 21:53:48,942 - INFO - [步骤 2/5] 为 '南京党建' 启动 UI 自动化...
2025-08-05 21:53:48,943 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-05 21:53:48,943 - INFO - 准备将链接发送到文件传输助手...
2025-08-05 21:53:48,943 - INFO - 正在查找微信主窗口...
2025-08-05 21:53:49,811 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-05 21:53:49,811 - INFO - 正在激活微信窗口...
2025-08-05 21:53:52,330 - INFO - 微信窗口已激活。
2025-08-05 21:53:52,331 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-05 22:07:50,041 - INFO - Imported existing <module 'comtypes.gen' from 'D:\\mynj\\mynj_env\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-05 22:07:50,041 - INFO - Using writeable comtypes cache directory: 'D:\mynj\mynj_env\Lib\site-packages\comtypes\gen'
2025-08-05 22:07:50,045 - INFO - ================================================================================
2025-08-05 22:07:50,045 - INFO - 🚀 微信公众号全自动爬取流程启动 🚀
2025-08-05 22:07:50,046 - INFO - ================================================================================
2025-08-05 22:07:50,046 - INFO - 版本: v3.0 - 全自动化版本
2025-08-05 22:07:50,046 - INFO - 设计用途: Windows任务计划程序自动执行
2025-08-05 22:07:50,046 - INFO - 执行时间: 2025-08-05 22:07:50
2025-08-05 22:07:50,046 - INFO - ================================================================================
2025-08-05 22:07:50,047 - INFO - 启动全新自动化爬取流程...
2025-08-05 22:07:50,047 - INFO - ================================================================================
2025-08-05 22:07:50,048 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-05 22:07:50,048 - INFO - ================================================================================
2025-08-05 22:07:50,048 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-05 22:07:50,131 - INFO - 找到有效目标 1: 钟山清风 - https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&...
2025-08-05 22:07:50,131 - INFO - 找到有效目标 2: 南京党建 - http://mp.weixin.qq.com/s?__biz=MzI2OTMwNzU5Nw==&m...
2025-08-05 22:07:50,131 - INFO - 找到有效目标 3: 南京发布 - http://mp.weixin.qq.com/s?__biz=MjM5MTczODg0MA==&m...
2025-08-05 22:07:50,131 - INFO - 共找到 3 个有效的公众号目标
2025-08-05 22:07:50,132 - INFO - 📋 共找到 3 个公众号，开始逐个处理...
2025-08-05 22:07:50,132 - INFO - ============================================================
2025-08-05 22:07:50,132 - INFO - 📍 处理第 1/3 个公众号: 钟山清风
2025-08-05 22:07:50,132 - INFO - ============================================================
2025-08-05 22:07:50,132 - INFO - [步骤 1/5] 为 '钟山清风' 创建独立的 Cookie 抓取器...
2025-08-05 22:07:50,132 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-05 22:07:50,132 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-05 22:07:50,132 - INFO - 步骤1: 正在准备网络环境...
2025-08-05 22:07:50,133 - INFO - === 开始重置网络状态 ===
2025-08-05 22:07:50,133 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-05 22:07:50,227 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-05 22:07:50,227 - INFO - 🔧 正在关闭系统代理设置...
2025-08-05 22:07:50,227 - INFO - 系统代理已成功关闭
2025-08-05 22:07:50,227 - INFO - ✅ 代理关闭操作
2025-08-05 22:07:50,227 - INFO - 🔗 正在验证网络连接...
2025-08-05 22:07:50,336 - INFO - ✅ 网络连接正常（无代理）- 测试网站: https://www.baidu.com
2025-08-05 22:07:50,337 - INFO - ✅ 网络状态重置验证完成
2025-08-05 22:07:50,337 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-05 22:07:50,337 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-05 22:07:50,338 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider_gitlab\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-05 22:07:51,968 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-05 22:07:51,969 - INFO - 🔄 Cookie抓取器进程已启动，PID: 31624
2025-08-05 22:07:51,970 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-05 22:07:54,970 - INFO - 等待代理服务启动...
2025-08-05 22:07:54,972 - INFO - ✅ 端口 8080 已开始监听
2025-08-05 22:07:55,060 - INFO - ✅ 代理服务已启动并正常工作
2025-08-05 22:07:55,060 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 31624)
2025-08-05 22:07:55,060 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-05 22:07:55,060 - INFO - [步骤 2/5] 为 '钟山清风' 启动 UI 自动化...
2025-08-05 22:07:55,061 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-05 22:07:55,061 - INFO - 准备将链接发送到文件传输助手...
2025-08-05 22:07:55,061 - INFO - 正在查找微信主窗口...
2025-08-05 22:08:06,012 - WARNING - 未找到 'WeChatMainWndForPC' 窗口，尝试备用方案...
2025-08-05 22:08:11,617 - ERROR - 未找到微信主窗口，请确保微信已登录并显示主界面。
2025-08-05 22:08:11,617 - ERROR - 发送链接失败，流程中止。
2025-08-05 22:08:11,617 - ERROR - ❌ 公众号 '钟山清风' UI 自动化触发失败，跳过此公众号
2025-08-05 22:08:11,617 - INFO - 🧹 开始清理抓取器资源...
2025-08-05 22:08:11,617 - INFO - 正在停止Cookie抓取器 (PID: 31624)...
2025-08-05 22:08:11,618 - INFO - Cookie抓取器已成功终止。
2025-08-05 22:08:11,618 - INFO - 正在验证并清理代理设置...
2025-08-05 22:08:11,618 - INFO - === 开始重置网络状态 ===
2025-08-05 22:08:11,618 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-05 22:08:11,710 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-05 22:08:11,710 - INFO - 🔧 正在关闭系统代理设置...
2025-08-05 22:08:11,710 - INFO - 系统代理已成功关闭
2025-08-05 22:08:11,710 - INFO - ✅ 代理关闭操作
2025-08-05 22:08:11,710 - INFO - 🔗 正在验证网络连接...
2025-08-05 22:08:12,816 - INFO - ✅ 网络连接正常（无代理）- 测试网站: https://www.baidu.com
2025-08-05 22:08:12,816 - INFO - ✅ 网络状态重置验证完成
2025-08-05 22:08:12,817 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-05 22:08:13,971 - INFO - ✅ 网络连接正常（无代理）- 测试网站: https://www.baidu.com
2025-08-05 22:08:13,972 - INFO - ✅ 网络连接验证正常
2025-08-05 22:08:13,973 - INFO - ============================================================
2025-08-05 22:08:13,974 - INFO - 📍 处理第 2/3 个公众号: 南京党建
2025-08-05 22:08:13,974 - INFO - ============================================================
2025-08-05 22:08:13,974 - INFO - [步骤 1/5] 为 '南京党建' 创建独立的 Cookie 抓取器...
2025-08-05 22:08:13,975 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-05 22:08:13,976 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-05 22:08:13,976 - INFO - 步骤1: 正在准备网络环境...
2025-08-05 22:08:13,977 - INFO - === 开始重置网络状态 ===
2025-08-05 22:08:13,977 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-05 22:08:14,105 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-05 22:08:14,105 - INFO - 🔧 正在关闭系统代理设置...
2025-08-05 22:08:14,105 - INFO - 系统代理已成功关闭
2025-08-05 22:08:14,105 - INFO - ✅ 代理关闭操作
2025-08-05 22:08:14,105 - INFO - 🔗 正在验证网络连接...
2025-08-05 22:08:14,202 - INFO - ✅ 网络连接正常（无代理）- 测试网站: https://www.baidu.com
2025-08-05 22:08:14,203 - INFO - ✅ 网络状态重置验证完成
2025-08-05 22:08:14,203 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-05 22:08:14,204 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-05 22:08:14,204 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider_gitlab\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-05 22:08:16,372 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-05 22:08:16,373 - INFO - 🔄 Cookie抓取器进程已启动，PID: 33784
2025-08-05 22:08:16,373 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-05 22:08:19,374 - INFO - 等待代理服务启动...
2025-08-05 22:08:19,374 - INFO - ✅ 端口 8080 已开始监听
2025-08-05 22:08:19,422 - INFO - ✅ 代理服务已启动并正常工作
2025-08-05 22:08:19,422 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 33784)
2025-08-05 22:08:19,422 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-05 22:08:19,422 - INFO - [步骤 2/5] 为 '南京党建' 启动 UI 自动化...
2025-08-05 22:08:19,422 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-05 22:08:19,423 - INFO - 准备将链接发送到文件传输助手...
2025-08-05 22:08:19,423 - INFO - 正在查找微信主窗口...
2025-08-05 22:08:26,526 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-05 22:08:26,526 - INFO - 正在激活微信窗口...
2025-08-05 22:08:29,038 - INFO - 微信窗口已激活。
2025-08-05 22:08:29,039 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-05 22:08:35,686 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-05 22:08:35,687 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-05 22:08:43,473 - INFO - Imported existing <module 'comtypes.gen' from 'D:\\mynj\\mynj_env\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-05 22:08:43,473 - INFO - Using writeable comtypes cache directory: 'D:\mynj\mynj_env\Lib\site-packages\comtypes\gen'
2025-08-05 22:08:43,478 - INFO - ================================================================================
2025-08-05 22:08:43,478 - INFO - 🚀 微信公众号全自动爬取流程启动 🚀
2025-08-05 22:08:43,478 - INFO - ================================================================================
2025-08-05 22:08:43,478 - INFO - 版本: v3.0 - 全自动化版本
2025-08-05 22:08:43,478 - INFO - 设计用途: Windows任务计划程序自动执行
2025-08-05 22:08:43,479 - INFO - 执行时间: 2025-08-05 22:08:43
2025-08-05 22:08:43,479 - INFO - ================================================================================
2025-08-05 22:08:43,479 - INFO - 启动全新自动化爬取流程...
2025-08-05 22:08:43,479 - INFO - ================================================================================
2025-08-05 22:08:43,479 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-05 22:08:43,479 - INFO - ================================================================================
2025-08-05 22:08:43,479 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-05 22:08:43,709 - INFO - 找到有效目标 1: 钟山清风 - https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&...
2025-08-05 22:08:43,709 - INFO - 找到有效目标 2: 南京党建 - http://mp.weixin.qq.com/s?__biz=MzI2OTMwNzU5Nw==&m...
2025-08-05 22:08:43,710 - INFO - 找到有效目标 3: 南京发布 - http://mp.weixin.qq.com/s?__biz=MjM5MTczODg0MA==&m...
2025-08-05 22:08:43,710 - INFO - 共找到 3 个有效的公众号目标
2025-08-05 22:08:43,710 - INFO - 📋 共找到 3 个公众号，开始逐个处理...
2025-08-05 22:08:43,710 - INFO - ============================================================
2025-08-05 22:08:43,710 - INFO - 📍 处理第 1/3 个公众号: 钟山清风
2025-08-05 22:08:43,710 - INFO - ============================================================
2025-08-05 22:08:43,710 - INFO - [步骤 1/5] 为 '钟山清风' 创建独立的 Cookie 抓取器...
2025-08-05 22:08:43,710 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-05 22:08:43,710 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-05 22:08:43,711 - INFO - 步骤1: 正在准备网络环境...
2025-08-05 22:08:43,711 - INFO - === 开始重置网络状态 ===
2025-08-05 22:08:43,711 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-05 22:08:43,798 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-05 22:08:43,798 - INFO - 🔧 正在关闭系统代理设置...
2025-08-05 22:08:43,798 - INFO - 系统代理已成功关闭
2025-08-05 22:08:43,798 - INFO - ✅ 代理关闭操作
2025-08-05 22:08:43,798 - INFO - 🔗 正在验证网络连接...
2025-08-05 22:08:44,892 - INFO - ✅ 网络连接正常（无代理）- 测试网站: https://www.baidu.com
2025-08-05 22:08:44,892 - INFO - ✅ 网络状态重置验证完成
2025-08-05 22:08:44,892 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-05 22:08:44,893 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-05 22:08:44,893 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider_gitlab\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-05 22:08:45,419 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-05 22:08:45,420 - INFO - 🔄 Cookie抓取器进程已启动，PID: 28992
2025-08-05 22:08:45,420 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-05 22:08:48,420 - INFO - 等待代理服务启动...
2025-08-05 22:08:48,421 - INFO - ✅ 端口 8080 已开始监听
2025-08-05 22:08:48,492 - INFO - ✅ 代理服务已启动并正常工作
2025-08-05 22:08:48,492 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 28992)
2025-08-05 22:08:48,492 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-05 22:08:48,492 - INFO - [步骤 2/5] 为 '钟山清风' 启动 UI 自动化...
2025-08-05 22:08:48,493 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-05 22:08:48,493 - INFO - 准备将链接发送到文件传输助手...
2025-08-05 22:08:48,493 - INFO - 正在查找微信主窗口...
2025-08-05 22:08:48,587 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-05 22:08:48,587 - INFO - 正在激活微信窗口...
2025-08-05 22:08:51,096 - INFO - 微信窗口已激活。
2025-08-05 22:08:51,096 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-05 22:08:57,785 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-05 22:08:57,786 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-05 22:09:01,120 - INFO - 正在查找聊天输入框...
2025-08-05 22:09:03,120 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-05 22:09:03,128 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-05 22:09:03,128 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-05 22:09:04,708 - INFO - 已将链接复制到剪贴板: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&mid=2247521212&idx=1&sn=2d7cae536e0ced5e4f59ded16b88ab30&chksm=cf77a61131b97056fa39d8d9863d17ebd6c23a14e37c3dfd8cdcc5bb00fe2c549e215b8928d8&scene=27#wechat_redirect
2025-08-05 22:09:06,995 - INFO - 链接已粘贴，正在发送...
2025-08-05 22:09:07,267 - INFO - 找到发送按钮，点击发送...
2025-08-05 22:09:08,069 - INFO - 链接已发送。
2025-08-05 22:09:11,076 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-05 22:09:13,171 - INFO - 已定位到最新的消息项，准备点击。
2025-08-05 22:09:13,902 - INFO - ✅ 成功点击最新链接。
2025-08-05 22:09:16,903 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-05 22:09:16,904 - INFO - 正在查找微信浏览器窗口...
2025-08-05 22:09:16,904 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-05 22:09:16,920 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-05 22:09:19,626 - INFO - 已成功激活焦点浏览器窗口
2025-08-05 22:09:21,127 - INFO - 正在检测SSL证书错误页面...
2025-08-05 22:09:33,456 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-05 22:09:33,456 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-05 22:09:33,456 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-05 22:09:33,456 - INFO - 正在执行第 1 次刷新操作...
2025-08-05 22:09:33,457 - INFO - 从文件中解析到有效Cookie数据。
2025-08-05 22:09:33,457 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-05 22:09:33,457 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-05 22:09:33,457 - INFO - 从文件中解析到有效Cookie数据。
2025-08-05 22:09:33,457 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-05 22:09:33,458 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-05 22:09:33,458 - INFO - [步骤 3/5] 等待 '钟山清风' 的 Cookie 数据...
2025-08-05 22:09:33,458 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-05 22:09:34,458 - INFO - 检测到Cookie文件已生成。
2025-08-05 22:09:34,459 - INFO - 从文件中解析到有效Cookie数据。
2025-08-05 22:09:34,459 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-05 22:09:34,459 - INFO - [步骤 4/5] 停止 '钟山清风' 的 Cookie 抓取器...
2025-08-05 22:09:34,459 - INFO - 🧹 开始清理抓取器资源...
2025-08-05 22:09:34,459 - INFO - 正在停止Cookie抓取器 (PID: 28992)...
2025-08-05 22:09:34,460 - INFO - Cookie抓取器已成功终止。
2025-08-05 22:09:34,460 - INFO - 正在验证并清理代理设置...
2025-08-05 22:09:34,461 - INFO - === 开始重置网络状态 ===
2025-08-05 22:09:34,461 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-05 22:09:34,585 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-05 22:09:34,585 - INFO - 🔧 正在关闭系统代理设置...
2025-08-05 22:09:34,585 - INFO - 系统代理已成功关闭
2025-08-05 22:09:34,585 - INFO - ✅ 代理关闭操作
2025-08-05 22:09:34,585 - INFO - 🔗 正在验证网络连接...
2025-08-05 22:09:34,670 - INFO - ✅ 网络连接正常（无代理）- 测试网站: https://www.baidu.com
2025-08-05 22:09:34,671 - INFO - ✅ 网络状态重置验证完成
2025-08-05 22:09:34,671 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-05 22:09:34,779 - INFO - ✅ 网络连接正常（无代理）- 测试网站: https://www.baidu.com
2025-08-05 22:09:34,780 - INFO - ✅ 网络连接验证正常
2025-08-05 22:09:37,780 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-05 22:09:37,781 - INFO - [步骤 5/5] 开始爬取 '钟山清风' 的文章...
2025-08-05 22:09:37,781 - INFO - 🔄 第 1/2 次尝试爬取...
2025-08-05 22:09:38,068 - INFO - ✅ Cookie验证成功，开始正式爬取...
2025-08-05 22:30:14,807 - INFO - Imported existing <module 'comtypes.gen' from 'D:\\mynj\\mynj_env\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-05 22:30:14,815 - INFO - Using writeable comtypes cache directory: 'D:\mynj\mynj_env\Lib\site-packages\comtypes\gen'
2025-08-05 22:30:14,819 - INFO - ================================================================================
2025-08-05 22:30:14,819 - INFO - 🚀 微信公众号全自动爬取流程启动 🚀
2025-08-05 22:30:14,819 - INFO - ================================================================================
2025-08-05 22:30:14,820 - INFO - 版本: v3.0 - 全自动化版本
2025-08-05 22:30:14,820 - INFO - 设计用途: Windows任务计划程序自动执行
2025-08-05 22:30:14,820 - INFO - 执行时间: 2025-08-05 22:30:14
2025-08-05 22:30:14,820 - INFO - ================================================================================
2025-08-05 22:30:14,820 - INFO - 启动全新自动化爬取流程...
2025-08-05 22:30:14,821 - INFO - ================================================================================
2025-08-05 22:30:14,821 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-05 22:30:14,821 - INFO - ================================================================================
2025-08-05 22:30:14,821 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-05 22:30:15,115 - INFO - 找到有效目标 1: 钟山清风 - https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&...
2025-08-05 22:30:15,115 - INFO - 找到有效目标 2: 南京党建 - http://mp.weixin.qq.com/s?__biz=MzI2OTMwNzU5Nw==&m...
2025-08-05 22:30:15,115 - INFO - 找到有效目标 3: 南京发布 - http://mp.weixin.qq.com/s?__biz=MjM5MTczODg0MA==&m...
2025-08-05 22:30:15,115 - INFO - 共找到 3 个有效的公众号目标
2025-08-05 22:30:15,115 - INFO - 📋 共找到 3 个公众号，开始逐个处理...
2025-08-05 22:30:15,116 - INFO - ============================================================
2025-08-05 22:30:15,116 - INFO - 📍 处理第 1/3 个公众号: 钟山清风
2025-08-05 22:30:15,116 - INFO - ============================================================
2025-08-05 22:30:15,116 - INFO - [步骤 1/5] 为 '钟山清风' 创建独立的 Cookie 抓取器...
2025-08-05 22:30:15,116 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-05 22:30:15,116 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-05 22:30:15,117 - INFO - 步骤1: 正在准备网络环境...
2025-08-05 22:30:15,117 - INFO - === 开始重置网络状态 ===
2025-08-05 22:30:15,117 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-05 22:30:15,215 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-05 22:30:15,215 - INFO - 🔧 正在关闭系统代理设置...
2025-08-05 22:30:15,215 - INFO - 系统代理已成功关闭
2025-08-05 22:30:15,215 - INFO - ✅ 代理关闭操作
2025-08-05 22:30:15,215 - INFO - 🔗 正在验证网络连接...
2025-08-05 22:30:15,370 - INFO - ✅ 网络连接正常（无代理）- 测试网站: https://www.baidu.com
2025-08-05 22:30:15,371 - INFO - ✅ 网络状态重置验证完成
2025-08-05 22:30:15,371 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-05 22:30:15,371 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-05 22:30:15,372 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider_gitlab\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-05 22:30:17,211 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-05 22:30:17,212 - INFO - 🔄 Cookie抓取器进程已启动，PID: 23352
2025-08-05 22:30:17,212 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-05 22:30:20,212 - INFO - 等待代理服务启动...
2025-08-05 22:30:20,213 - INFO - ✅ 端口 8080 已开始监听
2025-08-05 22:30:20,298 - INFO - ✅ 代理服务已启动并正常工作
2025-08-05 22:30:20,298 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 23352)
2025-08-05 22:30:20,299 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-05 22:30:20,299 - INFO - [步骤 2/5] 为 '钟山清风' 启动 UI 自动化...
2025-08-05 22:30:20,299 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-05 22:30:20,299 - INFO - 准备将链接发送到文件传输助手...
2025-08-05 22:30:20,299 - INFO - 正在查找微信主窗口...
2025-08-05 22:30:22,078 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-05 22:30:22,079 - INFO - 正在激活微信窗口...
2025-08-05 22:30:24,603 - INFO - 微信窗口已激活。
2025-08-05 22:30:24,603 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-05 22:30:31,316 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-05 22:30:31,317 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-05 22:30:34,651 - INFO - 正在查找聊天输入框...
2025-08-05 22:30:36,652 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-05 22:30:36,660 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-05 22:30:36,661 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-05 22:30:38,243 - INFO - 已将链接复制到剪贴板: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&mid=2247521212&idx=1&sn=2d7cae536e0ced5e4f59ded16b88ab30&chksm=cf77a61131b97056fa39d8d9863d17ebd6c23a14e37c3dfd8cdcc5bb00fe2c549e215b8928d8&scene=27#wechat_redirect
2025-08-05 22:30:40,529 - INFO - 链接已粘贴，正在发送...
2025-08-05 22:30:40,795 - INFO - 找到发送按钮，点击发送...
2025-08-05 22:30:41,601 - INFO - 链接已发送。
2025-08-05 22:30:44,602 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-05 22:30:46,699 - INFO - 已定位到最新的消息项，准备点击。
2025-08-05 22:30:47,435 - INFO - ✅ 成功点击最新链接。
2025-08-05 22:30:50,435 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-05 22:30:50,436 - INFO - 正在查找微信浏览器窗口...
2025-08-05 22:30:50,436 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-05 22:30:50,455 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-05 22:30:53,162 - INFO - 已成功激活焦点浏览器窗口
2025-08-05 22:30:54,662 - INFO - 正在检测SSL证书错误页面...
2025-08-05 22:31:06,788 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-05 22:31:06,788 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-05 22:31:06,788 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-05 22:31:06,788 - INFO - 正在执行第 1 次刷新操作...
2025-08-05 22:31:06,788 - WARNING - 在文件中未找到有效的Cookie数据。
2025-08-05 22:31:06,789 - INFO - 正在查找微信浏览器窗口...
2025-08-05 22:31:06,789 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-05 22:31:06,792 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-05 22:31:09,497 - INFO - 已成功激活焦点浏览器窗口
2025-08-05 22:31:09,998 - INFO - 正在查找微信浏览器窗口...
2025-08-05 22:31:09,999 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-05 22:31:10,009 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-05 22:31:12,714 - INFO - 已成功激活焦点浏览器窗口
2025-08-05 22:31:14,215 - INFO - 正在检测SSL证书错误页面...
2025-08-05 22:31:26,894 - INFO - 已发送F5刷新指令到浏览器窗口
2025-08-05 22:31:26,895 - INFO - 等待页面刷新完成... (2.5秒)
2025-08-05 22:31:29,396 - INFO - 从文件中解析到有效Cookie数据。
2025-08-05 22:31:29,396 - INFO - 🎉 检测到抓包成功！在第 1 次刷新后停止
2025-08-05 22:31:29,397 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-05 22:31:29,397 - INFO - 从文件中解析到有效Cookie数据。
2025-08-05 22:31:29,397 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-05 22:31:29,397 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-05 22:31:29,398 - INFO - [步骤 3/5] 等待 '钟山清风' 的 Cookie 数据...
2025-08-05 22:31:29,405 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-05 22:31:30,405 - INFO - 检测到Cookie文件已生成。
2025-08-05 22:31:30,406 - INFO - 从文件中解析到有效Cookie数据。
2025-08-05 22:31:30,406 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-05 22:31:30,406 - INFO - [步骤 4/5] 停止 '钟山清风' 的 Cookie 抓取器...
2025-08-05 22:31:30,406 - INFO - 🧹 开始清理抓取器资源...
2025-08-05 22:31:30,406 - INFO - 正在停止Cookie抓取器 (PID: 23352)...
2025-08-05 22:31:30,407 - INFO - Cookie抓取器已成功终止。
2025-08-05 22:31:30,407 - INFO - 正在验证并清理代理设置...
2025-08-05 22:31:30,407 - INFO - === 开始重置网络状态 ===
2025-08-05 22:31:30,407 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-05 22:31:30,530 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-05 22:31:30,531 - INFO - 🔧 正在关闭系统代理设置...
2025-08-05 22:31:30,531 - INFO - 系统代理已成功关闭
2025-08-05 22:31:30,531 - INFO - ✅ 代理关闭操作
2025-08-05 22:31:30,531 - INFO - 🔗 正在验证网络连接...
2025-08-05 22:31:30,643 - INFO - ✅ 网络连接正常（无代理）- 测试网站: https://www.baidu.com
2025-08-05 22:31:30,644 - INFO - ✅ 网络状态重置验证完成
2025-08-05 22:31:30,645 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-05 22:31:30,757 - INFO - ✅ 网络连接正常（无代理）- 测试网站: https://www.baidu.com
2025-08-05 22:31:30,758 - INFO - ✅ 网络连接验证正常
2025-08-05 22:31:33,758 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-05 22:31:33,759 - INFO - [步骤 5/5] 开始爬取 '钟山清风' 的文章...
2025-08-05 22:31:33,759 - INFO - 🔄 第 1/2 次尝试爬取...
2025-08-05 22:31:34,016 - INFO - ✅ Cookie验证成功，开始正式爬取...
2025-08-05 22:39:39,002 - INFO - Imported existing <module 'comtypes.gen' from 'D:\\mynj\\mynj_env\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-05 22:39:39,002 - INFO - Using writeable comtypes cache directory: 'D:\mynj\mynj_env\Lib\site-packages\comtypes\gen'
2025-08-05 22:39:39,006 - INFO - ================================================================================
2025-08-05 22:39:39,006 - INFO - 🚀 微信公众号全自动爬取流程启动 🚀
2025-08-05 22:39:39,006 - INFO - ================================================================================
2025-08-05 22:39:39,006 - INFO - 版本: v3.0 - 全自动化版本
2025-08-05 22:39:39,006 - INFO - 设计用途: Windows任务计划程序自动执行
2025-08-05 22:39:39,007 - INFO - 执行时间: 2025-08-05 22:39:39
2025-08-05 22:39:39,007 - INFO - ================================================================================
2025-08-05 22:39:39,007 - INFO - 🔍 测试数据库连接...
2025-08-05 22:39:39,015 - INFO - ✅ 数据库连接成功: 127.0.0.1:3306/xuanfa
2025-08-05 22:39:39,017 - INFO - ✅ 数据库连接成功！当前有 64 篇文章
2025-08-05 22:39:39,017 - INFO - 💾 将启用数据库实时保存功能
2025-08-05 22:39:39,017 - INFO - 数据库连接已关闭
2025-08-05 22:39:39,017 - INFO - 启动全新自动化爬取流程...
2025-08-05 22:39:39,018 - INFO - ✅ 数据库连接成功: 127.0.0.1:3306/xuanfa
2025-08-05 22:39:39,019 - INFO - ✅ 数据库连接成功！当前有 64 篇文章
2025-08-05 22:39:39,019 - INFO - 数据库连接已关闭
2025-08-05 22:39:39,019 - INFO - ================================================================================
2025-08-05 22:39:39,019 - INFO - 🚀 多公众号全新自动化流程启动 🚀
2025-08-05 22:39:39,019 - INFO - ================================================================================
2025-08-05 22:39:39,019 - INFO - 正在从 target_articles.xlsx 读取所有目标URL...
2025-08-05 22:39:39,146 - INFO - 找到有效目标 1: 钟山清风 - https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&...
2025-08-05 22:39:39,146 - INFO - 找到有效目标 2: 南京党建 - http://mp.weixin.qq.com/s?__biz=MzI2OTMwNzU5Nw==&m...
2025-08-05 22:39:39,147 - INFO - 找到有效目标 3: 南京发布 - http://mp.weixin.qq.com/s?__biz=MjM5MTczODg0MA==&m...
2025-08-05 22:39:39,147 - INFO - 共找到 3 个有效的公众号目标
2025-08-05 22:39:39,147 - INFO - 📋 共找到 3 个公众号，开始逐个处理...
2025-08-05 22:39:39,147 - INFO - ============================================================
2025-08-05 22:39:39,147 - INFO - 📍 处理第 1/3 个公众号: 钟山清风
2025-08-05 22:39:39,148 - INFO - ============================================================
2025-08-05 22:39:39,148 - INFO - [步骤 1/5] 为 '钟山清风' 创建独立的 Cookie 抓取器...
2025-08-05 22:39:39,148 - INFO - 已删除旧的日志文件: wechat_keys.txt
2025-08-05 22:39:39,148 - INFO - 🚀 开始启动Cookie抓取器...
2025-08-05 22:39:39,148 - INFO - 步骤1: 正在准备网络环境...
2025-08-05 22:39:39,148 - INFO - === 开始重置网络状态 ===
2025-08-05 22:39:39,149 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-05 22:39:39,237 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-05 22:39:39,237 - INFO - 🔧 正在关闭系统代理设置...
2025-08-05 22:39:39,237 - INFO - 系统代理已成功关闭
2025-08-05 22:39:39,237 - INFO - ✅ 代理关闭操作
2025-08-05 22:39:39,237 - INFO - 🔗 正在验证网络连接...
2025-08-05 22:39:39,349 - INFO - ✅ 网络连接正常（无代理）- 测试网站: https://www.baidu.com
2025-08-05 22:39:39,349 - INFO - ✅ 网络状态重置验证完成
2025-08-05 22:39:39,350 - INFO - 步骤2: 正在备份原始网络配置...
2025-08-05 22:39:39,350 - INFO - 已备份原始代理设置: {'enable': False, 'server': ''}
2025-08-05 22:39:39,350 - INFO - 步骤3: 正在启动命令: mitmdump -s D:\mynj\wechat_spider_gitlab\cookie_extractor.py --listen-port 8080 --ssl-insecure
2025-08-05 22:39:39,993 - INFO - ✅ mitmdump版本: Mitmproxy: 12.1.1
Python:    3.13.5
OpenSSL:   OpenSSL 3.4.1 11 Feb 2025
Platform:  Windows-11-10.0.22631-SP0
2025-08-05 22:39:39,993 - INFO - 🔄 Cookie抓取器进程已启动，PID: 28180
2025-08-05 22:39:39,994 - INFO - 步骤4: 等待代理服务启动... (最多30秒)
2025-08-05 22:39:42,994 - INFO - 等待代理服务启动...
2025-08-05 22:39:42,995 - INFO - ✅ 端口 8080 已开始监听
2025-08-05 22:39:43,039 - INFO - ✅ 代理服务已启动并正常工作
2025-08-05 22:39:43,040 - INFO - ✅ Cookie抓取器已成功启动并运行正常 (PID: 28180)
2025-08-05 22:39:43,040 - INFO - ✅ Cookie 抓取器已在后台运行。
2025-08-05 22:39:43,040 - INFO - [步骤 2/5] 为 '钟山清风' 启动 UI 自动化...
2025-08-05 22:39:43,041 - INFO - --- 步骤 1: 正在发送链接到文件传输助手 ---
2025-08-05 22:39:43,041 - INFO - 准备将链接发送到文件传输助手...
2025-08-05 22:39:43,042 - INFO - 正在查找微信主窗口...
2025-08-05 22:39:43,119 - INFO - 成功找到微信窗口 (ClassName='WeChatMainWndForPC')
2025-08-05 22:39:43,119 - INFO - 正在激活微信窗口...
2025-08-05 22:39:45,636 - INFO - 微信窗口已激活。
2025-08-05 22:39:45,636 - INFO - 尝试通过搜索框查找并进入'文件传输助手'...
2025-08-05 22:39:52,347 - INFO - 已通过搜索进入'文件传输助手'。
2025-08-05 22:39:52,348 - INFO - 清空搜索框并将焦点转移到聊天区域...
2025-08-05 22:39:55,682 - INFO - 正在查找聊天输入框...
2025-08-05 22:39:57,683 - INFO - 方法1: 查找所有EditControl，排除搜索框...
2025-08-05 22:39:57,695 - INFO - 方法2: 直接点击聊天输入区域...
2025-08-05 22:39:57,696 - INFO - 点击聊天输入区域坐标: (960, 756)
2025-08-05 22:39:59,270 - INFO - 已将链接复制到剪贴板: https://mp.weixin.qq.com/s?__biz=Mzg3MzcwMjI5NQ==&mid=2247521212&idx=1&sn=2d7cae536e0ced5e4f59ded16b88ab30&chksm=cf77a61131b97056fa39d8d9863d17ebd6c23a14e37c3dfd8cdcc5bb00fe2c549e215b8928d8&scene=27#wechat_redirect
2025-08-05 22:40:01,556 - INFO - 链接已粘贴，正在发送...
2025-08-05 22:40:01,802 - INFO - 找到发送按钮，点击发送...
2025-08-05 22:40:02,617 - INFO - 链接已发送。
2025-08-05 22:40:05,618 - INFO - --- 步骤 2: 链接已发送，准备查找并点击最新消息 ---
2025-08-05 22:40:07,705 - INFO - 已定位到最新的消息项，准备点击。
2025-08-05 22:40:08,432 - INFO - ✅ 成功点击最新链接。
2025-08-05 22:40:11,433 - INFO - --- 步骤 2.5: 检测并处理SSL证书错误页面 ---
2025-08-05 22:40:11,433 - INFO - 正在查找微信浏览器窗口...
2025-08-05 22:40:11,433 - INFO - 尝试使用GetFocusedControl查找当前焦点窗口...
2025-08-05 22:40:11,440 - INFO - 通过焦点检测找到浏览器窗口: '微信' (Chrome_WidgetWin_0)
2025-08-05 22:40:14,148 - INFO - 已成功激活焦点浏览器窗口
2025-08-05 22:40:15,649 - INFO - 正在检测SSL证书错误页面...
2025-08-05 22:40:28,010 - INFO - 未检测到SSL证书错误页面，继续正常流程
2025-08-05 22:40:28,010 - INFO - --- 步骤 3: 开始执行自动刷新 ---
2025-08-05 22:40:28,010 - INFO - 🔍 启用了抓包检测，将在成功抓包后自动停止刷新
2025-08-05 22:40:28,010 - INFO - 正在执行第 1 次刷新操作...
2025-08-05 22:40:28,011 - INFO - 从文件中解析到有效Cookie数据。
2025-08-05 22:40:28,011 - INFO - 🎉 检测到抓包成功！在第 1 次刷新前停止刷新操作
2025-08-05 22:40:28,011 - INFO - ✅ 自动刷新提前结束，开始进行阅读量爬取
2025-08-05 22:40:28,011 - INFO - 从文件中解析到有效Cookie数据。
2025-08-05 22:40:28,011 - INFO - ✅ 自动刷新因抓包成功而提前结束
2025-08-05 22:40:28,011 - INFO - ✅ UI 自动化已成功触发链接打开。
2025-08-05 22:40:28,012 - INFO - [步骤 3/5] 等待 '钟山清风' 的 Cookie 数据...
2025-08-05 22:40:28,012 - INFO - 正在等待Cookie数据写入 'wechat_keys.txt'... (超时: 120秒)
2025-08-05 22:40:29,012 - INFO - 检测到Cookie文件已生成。
2025-08-05 22:40:29,013 - INFO - 从文件中解析到有效Cookie数据。
2025-08-05 22:40:29,014 - INFO - ✅ 成功获取并验证了新的 Cookie。
2025-08-05 22:40:29,014 - INFO - [步骤 4/5] 停止 '钟山清风' 的 Cookie 抓取器...
2025-08-05 22:40:29,014 - INFO - 🧹 开始清理抓取器资源...
2025-08-05 22:40:29,015 - INFO - 正在停止Cookie抓取器 (PID: 28180)...
2025-08-05 22:40:29,017 - INFO - Cookie抓取器已成功终止。
2025-08-05 22:40:29,017 - INFO - 正在验证并清理代理设置...
2025-08-05 22:40:29,018 - INFO - === 开始重置网络状态 ===
2025-08-05 22:40:29,018 - INFO - 🔍 正在检查并结束现有代理进程...
2025-08-05 22:40:29,143 - INFO - 未发现运行中的mitmdump进程，跳过进程结束步骤
2025-08-05 22:40:29,144 - INFO - 🔧 正在关闭系统代理设置...
2025-08-05 22:40:29,144 - INFO - 系统代理已成功关闭
2025-08-05 22:40:29,144 - INFO - ✅ 代理关闭操作
2025-08-05 22:40:29,144 - INFO - 🔗 正在验证网络连接...
2025-08-05 22:40:29,172 - INFO - ✅ 网络连接正常（无代理）- 测试网站: https://www.baidu.com
2025-08-05 22:40:29,172 - INFO - ✅ 网络状态重置验证完成
2025-08-05 22:40:29,172 - INFO - ✅ 代理已完全关闭，网络状态已清理
2025-08-05 22:40:29,220 - INFO - ✅ 网络连接正常（无代理）- 测试网站: https://www.baidu.com
2025-08-05 22:40:29,221 - INFO - ✅ 网络连接验证正常
2025-08-05 22:40:32,222 - INFO - ✅ Cookie 抓取器已停止，系统代理已恢复。
2025-08-05 22:40:32,223 - INFO - [步骤 5/5] 开始爬取 '钟山清风' 的文章...
2025-08-05 22:40:32,223 - INFO - 🔄 第 1/2 次尝试爬取...
2025-08-05 22:40:32,240 - INFO - ✅ 数据库连接成功: 127.0.0.1:3306/xuanfa
2025-08-05 22:40:32,508 - INFO - ✅ Cookie验证成功，开始正式爬取...
2025-08-05 22:40:41,852 - INFO - ✅ 文章插入成功: 六合区纪委监委：督促完善供水设施   化解农村用水难题 (ID: 2025080522402391)
2025-08-05 22:40:54,660 - INFO - ✅ 文章插入成功: 节礼去奢入俭   回归节日本味 (ID: 2025080522400350)
2025-08-05 22:41:07,501 - INFO - ✅ 文章插入成功: 雨花台区纪委监委：精准监督 推动兜牢民生底线 (ID: 2025080522413495)
2025-08-05 22:41:19,560 - INFO - ✅ 文章插入成功: 让担当作为蔚然成风 (ID: 2025080522415999)
