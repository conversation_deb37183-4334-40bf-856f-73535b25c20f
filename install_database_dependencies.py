# coding:utf-8
# install_database_dependencies.py
"""
数据库依赖安装脚本
自动安装数据库相关的Python包
"""

import subprocess
import sys
import os

def run_command(command):
    """执行命令并返回结果"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, encoding='utf-8')
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"🐍 Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("❌ 需要Python 3.7或更高版本")
        return False
    
    print("✅ Python版本符合要求")
    return True

def install_package(package_name):
    """安装Python包"""
    print(f"📦 正在安装 {package_name}...")
    
    # 尝试使用pip安装
    success, stdout, stderr = run_command(f"pip install {package_name}")
    
    if success:
        print(f"✅ {package_name} 安装成功")
        return True
    else:
        print(f"❌ {package_name} 安装失败")
        print(f"错误信息: {stderr}")
        
        # 尝试使用pip3
        print(f"🔄 尝试使用pip3安装 {package_name}...")
        success, stdout, stderr = run_command(f"pip3 install {package_name}")
        
        if success:
            print(f"✅ {package_name} 安装成功 (使用pip3)")
            return True
        else:
            print(f"❌ {package_name} 安装失败 (pip3)")
            print(f"错误信息: {stderr}")
            return False

def check_package_installed(package_name):
    """检查包是否已安装"""
    try:
        __import__(package_name)
        return True
    except ImportError:
        return False

def install_database_dependencies():
    """安装数据库相关依赖"""
    print("🚀 开始安装数据库依赖包")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        return False
    
    # 需要安装的包
    packages = [
        'pymysql>=1.0.0',
    ]
    
    # 可选包（如果需要的话）
    optional_packages = [
        'cryptography',  # 用于更安全的数据库连接
        'sqlalchemy',    # 如果需要ORM功能
    ]
    
    success_count = 0
    total_count = len(packages)
    
    print(f"\n📋 需要安装的必需包: {total_count} 个")
    
    # 安装必需包
    for package in packages:
        package_name = package.split('>=')[0].split('==')[0]
        
        # 检查是否已安装
        if check_package_installed(package_name):
            print(f"✅ {package_name} 已安装，跳过")
            success_count += 1
            continue
        
        if install_package(package):
            success_count += 1
    
    print(f"\n📊 必需包安装结果: {success_count}/{total_count}")
    
    # 询问是否安装可选包
    if success_count == total_count:
        print("\n🎯 所有必需包安装完成！")
        
        install_optional = input("\n是否安装可选包 (cryptography, sqlalchemy)? (y/n): ").strip().lower()
        
        if install_optional == 'y':
            print("\n📋 安装可选包...")
            for package in optional_packages:
                package_name = package.split('>=')[0].split('==')[0]
                
                if check_package_installed(package_name):
                    print(f"✅ {package_name} 已安装，跳过")
                    continue
                
                install_package(package)
    
    return success_count == total_count

def test_database_connection():
    """测试数据库连接"""
    print("\n🔍 测试数据库连接...")
    
    try:
        import pymysql
        print("✅ pymysql 导入成功")
        
        # 尝试连接数据库
        print("🔗 尝试连接数据库...")
        
        connection = pymysql.connect(
            host='127.0.0.1',
            port=3306,
            user='root',
            password='root',
            database='xuanfa',
            charset='utf8mb4'
        )
        
        print("✅ 数据库连接成功！")
        connection.close()
        return True
        
    except ImportError as e:
        print(f"❌ 导入pymysql失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        print("请检查:")
        print("  1. MySQL服务是否启动")
        print("  2. 数据库配置是否正确")
        print("  3. 数据库 'xuanfa' 是否存在")
        return False

def main():
    """主函数"""
    print("🎯 微信公众号爬虫数据库依赖安装工具")
    print("=" * 60)
    
    # 安装依赖
    if install_database_dependencies():
        print("\n🎉 依赖安装完成！")
        
        # 测试数据库连接
        if test_database_connection():
            print("\n✅ 数据库功能准备就绪！")
            print("\n📝 接下来你可以:")
            print("  1. 运行 python database_crawler_example.py 开始爬取")
            print("  2. 查看 database_config.py 修改数据库配置")
            print("  3. 使用 DatabaseManager 类进行数据库操作")
        else:
            print("\n⚠️ 依赖安装成功，但数据库连接测试失败")
            print("请检查数据库配置后再试")
    else:
        print("\n❌ 依赖安装失败，请检查网络连接和Python环境")

if __name__ == "__main__":
    main()
