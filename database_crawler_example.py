# coding:utf-8
# database_crawler_example.py
"""
带数据库功能的微信公众号爬虫示例
演示如何使用增强版爬虫将数据实时保存到数据库
"""

import logging
import sys
from enhanced_wx_crawler import EnhancedWxCrawler
from database_manager import DatabaseManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('database_crawler.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

def main():
    """主函数"""
    print("🚀 启动带数据库功能的微信公众号爬虫")
    print("=" * 60)
    
    # 数据库配置
    db_config = {
        'host': '127.0.0.1',
        'port': 3306,
        'user': 'root',
        'password': 'root',
        'database': 'xuanfa'
    }
    
    # 测试数据库连接
    print("🔍 测试数据库连接...")
    try:
        with DatabaseManager(**db_config) as db:
            count = db.get_articles_count()
            print(f"✅ 数据库连接成功！当前数据库中有 {count} 篇文章")
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        print("请检查数据库配置和连接")
        return
    
    # 爬虫参数配置
    print("\n📝 请输入爬虫参数:")
    
    # 从用户输入或配置文件获取参数
    appmsg_token = input("请输入 appmsg_token: ").strip()
    if not appmsg_token:
        print("❌ appmsg_token 不能为空")
        return
    
    biz = input("请输入 biz: ").strip()
    if not biz:
        print("❌ biz 不能为空")
        return
    
    cookie = input("请输入 cookie: ").strip()
    if not cookie:
        print("❌ cookie 不能为空")
        return
    
    unit_name = input("请输入公众号名称 (单位名称): ").strip()
    if not unit_name:
        unit_name = "未知公众号"
    
    # 页数配置
    try:
        begin_page = int(input("请输入起始页数 (默认0): ") or "0")
        end_page = int(input("请输入结束页数 (默认5): ") or "5")
    except ValueError:
        print("❌ 页数必须是数字")
        return
    
    # 是否获取文章内容
    get_content_input = input("是否获取文章内容? (y/n, 默认y): ").strip().lower()
    get_content = get_content_input != 'n'
    
    print(f"\n🎯 开始爬取公众号: {unit_name}")
    print(f"📄 页数范围: {begin_page} - {end_page}")
    print(f"📖 获取内容: {'是' if get_content else '否'}")
    print(f"💾 数据库保存: 是")
    print("=" * 60)
    
    # 创建爬虫实例
    try:
        crawler = EnhancedWxCrawler(
            appmsg_token=appmsg_token,
            biz=biz,
            cookie=cookie,
            begin_page_index=begin_page,
            end_page_index=end_page,
            save_to_file=True,  # 同时保存到文件
            get_content=get_content,
            unit_name=unit_name,
            save_to_db=True,  # 启用数据库保存
            db_config=db_config
        )
        
        # 开始爬取
        articles = crawler.run()
        
        # 显示结果摘要
        crawler.print_summary()
        
        # 显示数据库统计
        print("\n💾 数据库统计:")
        try:
            with DatabaseManager(**db_config) as db:
                total_count = db.get_articles_count()
                print(f"📊 数据库中总文章数: {total_count}")
        except Exception as e:
            print(f"❌ 获取数据库统计失败: {e}")
        
        print("\n🎉 爬取任务完成！")
        
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断爬取")
    except Exception as e:
        print(f"❌ 爬取过程中出错: {e}")
        logging.error(f"爬取错误: {e}", exc_info=True)

def test_database_operations():
    """测试数据库操作功能"""
    print("🧪 测试数据库操作功能")
    print("=" * 40)
    
    db_config = {
        'host': '127.0.0.1',
        'port': 3306,
        'user': 'root',
        'password': 'root',
        'database': 'xuanfa'
    }
    
    try:
        with DatabaseManager(**db_config) as db:
            # 测试连接
            print("✅ 数据库连接测试通过")
            
            # 获取当前文章数量
            count = db.get_articles_count()
            print(f"📊 当前数据库中有 {count} 篇文章")
            
            # 测试插入一条示例数据
            test_article = {
                'title': '测试文章标题',
                'content': '这是一篇测试文章的内容...',
                'url': 'https://mp.weixin.qq.com/s/test123456',
                'pub_time': '2025-08-05 22:00:00',
                'crawl_time': '2025-08-05 22:30:00',
                'unit_name': '测试公众号',
                'view_count': 1000
            }
            
            # 检查是否已存在
            if not db.check_article_exists(test_article['url']):
                success = db.insert_article(test_article)
                if success:
                    print("✅ 测试文章插入成功")
                    new_count = db.get_articles_count()
                    print(f"📊 插入后数据库中有 {new_count} 篇文章")
                else:
                    print("❌ 测试文章插入失败")
            else:
                print("⚠️ 测试文章已存在，跳过插入")
            
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")

if __name__ == "__main__":
    print("请选择操作:")
    print("1. 运行爬虫 (带数据库保存)")
    print("2. 测试数据库操作")
    
    choice = input("请输入选择 (1/2): ").strip()
    
    if choice == "1":
        main()
    elif choice == "2":
        test_database_operations()
    else:
        print("❌ 无效选择")
